# Deployment Guide for Product Compatibility Analysis Frontend

## AWS Amplify Deployment

### Prerequisites

1. AWS Account with appropriate permissions
2. AWS CLI configured
3. Node.js 16.x or later

### Step 1: Prepare Repository

```bash
# Initialize git repository if not already done
cd /home/<USER>/projects/auto_in_scope/frontend
git init
git add .
git commit -m "Initial frontend implementation"

# Option A: Push to AWS CodeCommit
aws codecommit create-repository --repository-name compatibility-frontend
git remote add origin https://git-codecommit.YOUR_REGION.amazonaws.com/v1/repos/compatibility-frontend
git push -u origin main

# Option B: Push to GitHub
# Create repository on GitHub first, then:
git remote add origin https://github.com/YOUR_USERNAME/compatibility-frontend.git
git push -u origin main
```

### Step 2: Set up AWS Amplify

#### Method 1: AWS Console (Recommended)

1. Go to AWS Amplify Console
2. Click "New app" → "Host web app"
3. Choose your source (GitHub/CodeCommit)
4. Select the repository: `compatibility-frontend`
5. Branch: `main`
6. App name: `compatibility-analysis-frontend`

#### Build Settings Configuration

Use the provided `amplify.yml` file, or configure manually:

```yaml
version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - npm ci
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: build
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    appRoot: frontend
```

#### Environment Variables

Set these in Amplify Console → App Settings → Environment variables:

```
REACT_APP_API_URL=https://your-api-gateway-url.execute-api.us-east-1.amazonaws.com/prod/api
GENERATE_SOURCEMAP=false
```

### Step 3: Configure Custom Domain (Optional)

1. In Amplify Console, go to Domain management
2. Add domain name
3. Configure DNS settings as instructed
4. Wait for SSL certificate provisioning

### Method 2: Amplify CLI

```bash
# Install Amplify CLI globally
npm install -g @aws-amplify/cli

# Configure Amplify
amplify configure

# Initialize Amplify project
cd /home/<USER>/projects/auto_in_scope/frontend
amplify init

# Add hosting
amplify add hosting
# Choose: Amazon CloudFront and S3

# Deploy
amplify publish
```

### Step 4: Backend Integration

Ensure your backend API Gateway has CORS configured for the Amplify domain:

```json
{
  "cors": {
    "allowOrigins": [
      "https://your-amplify-domain.amplifyapp.com",
      "http://localhost:3000"
    ],
    "allowMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    "allowHeaders": ["Content-Type", "Authorization", "X-Requested-With"]
  }
}
```

### Step 5: Monitoring and Logging

1. Enable Amplify access logs
2. Set up CloudWatch monitoring
3. Configure error reporting (optional: Sentry integration)

### CI/CD Pipeline

The deployment will automatically trigger on:
- Push to main branch
- Pull request merges
- Manual deployment from console

### Rollback Strategy

```bash
# Via Amplify Console
# Go to App → Deployments → Select previous version → Promote to production

# Via CLI
amplify env checkout prod
amplify status
amplify publish --invalidateCloudFront
```

### Production Checklist

- [ ] Environment variables configured
- [ ] Custom domain set up (if needed)
- [ ] SSL certificate active
- [ ] CORS configured on backend
- [ ] Error monitoring enabled
- [ ] Performance monitoring enabled
- [ ] Backup strategy in place

### Troubleshooting

#### Common Build Issues

1. **Node version mismatch**:
   ```yaml
   # Add to amplify.yml preBuild phase
   - nvm use 18
   ```

2. **Memory issues during build**:
   ```yaml
   # Add to amplify.yml build phase
   - export NODE_OPTIONS="--max-old-space-size=4096"
   ```

3. **Environment variable not loading**:
   - Verify variable names start with `REACT_APP_`
   - Check variables are set in Amplify Console
   - Trigger new build after adding variables

#### Runtime Issues

1. **API connection failed**:
   - Check REACT_APP_API_URL is correct
   - Verify CORS settings on backend
   - Check network connectivity

2. **File upload issues**:
   - Verify S3 bucket permissions
   - Check file size limits
   - Validate CORS on S3 bucket

### Security Considerations

1. **Environment Variables**: Never commit sensitive data to repository
2. **HTTPS Only**: Ensure all communication uses HTTPS
3. **Content Security Policy**: Configure CSP headers
4. **Access Control**: Limit Amplify app access if needed

### Performance Optimization

1. **Build optimization**:
   - Enable build caching in Amplify
   - Use code splitting in React
   - Optimize bundle size

2. **Runtime performance**:
   - Enable CloudFront compression
   - Set appropriate cache headers
   - Use Amplify performance monitoring

### Cost Management

1. **Monitor usage**: Track bandwidth and storage costs
2. **Optimize resources**: Remove unused dependencies
3. **Set up billing alerts**: Configure AWS cost alerts

### Maintenance

1. **Regular updates**: Keep dependencies updated
2. **Security patches**: Monitor for security vulnerabilities
3. **Performance monitoring**: Regular performance reviews
4. **Backup strategy**: Regular code and configuration backups