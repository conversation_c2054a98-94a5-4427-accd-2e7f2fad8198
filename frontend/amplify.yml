version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - echo "Node version:" && node --version
            - echo "NPM version:" && npm --version
            - echo "Current directory:" && pwd
            - echo "Directory contents:" && ls -la
            - npm ci
        build:
          commands:
            - echo "Starting build process..."
            - npm run build
            - echo "Build completed. Checking build directory:"
            - ls -la build/
            - echo "Build directory contents:"
            - find build -type f | head -20
      artifacts:
        baseDirectory: build
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    appRoot: .