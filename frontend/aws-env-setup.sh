#!/bin/bash

# AWS Environment Variables Setup Script
# 使用此脚本设置临时的AWS凭证

echo "🔐 AWS Environment Variables Setup"
echo "=================================="

# 提示用户输入AWS凭证
read -p "Enter your AWS Access Key ID: " AWS_ACCESS_KEY_ID
read -s -p "Enter your AWS Secret Access Key: " AWS_SECRET_ACCESS_KEY
echo
read -p "Enter your AWS Default Region [us-east-1]: " AWS_REGION
AWS_REGION=${AWS_REGION:-us-east-1}

# 设置环境变量
export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID"
export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY"
export AWS_DEFAULT_REGION="$AWS_REGION"

# 验证配置
echo ""
echo "✅ Environment variables set!"
echo "Testing AWS connection..."

if aws sts get-caller-identity >/dev/null 2>&1; then
    echo "✅ AWS CLI configured successfully!"
    echo "Current AWS Identity:"
    aws sts get-caller-identity
    echo ""
    echo "🚀 You can now run: ./deploy.sh"
else
    echo "❌ AWS configuration failed. Please check your credentials."
fi

# 保存到当前会话
echo ""
echo "💡 These credentials are set for this session only."
echo "To make them permanent, add them to your ~/.bashrc or ~/.profile:"
echo ""
echo "export AWS_ACCESS_KEY_ID=\"$AWS_ACCESS_KEY_ID\""
echo "export AWS_SECRET_ACCESS_KEY=\"$AWS_SECRET_ACCESS_KEY\""
echo "export AWS_DEFAULT_REGION=\"$AWS_REGION\""