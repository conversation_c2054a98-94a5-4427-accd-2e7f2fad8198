{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["amplify:*", "codecommit:*", "iam:CreateRole", "iam:CreatePolicy", "iam:AttachRolePolicy", "iam:PutRolePolicy", "iam:PassRole", "iam:GetRole", "iam:GetPolicy", "iam:ListRoles", "iam:ListPolicies", "cloudformation:*", "s3:CreateBucket", "s3:GetBucketLocation", "s3:ListBucket", "s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:GetBucketPolicy", "s3:PutBucketPolicy", "lambda:*", "apigateway:*", "logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents", "logs:DescribeLogGroups", "logs:DescribeLogStreams"], "Resource": "*"}]}