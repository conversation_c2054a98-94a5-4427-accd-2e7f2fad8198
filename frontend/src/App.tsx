import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import { Layout, Menu, Typography, Space } from 'antd';
import { 
  PlusOutlined, 
  TableOutlined, 
  RobotOutlined
} from '@ant-design/icons';
import TaskSubmissionForm from './components/TaskSubmissionForm';
import TaskViewPage from './components/TaskViewPage';
import './App.css';

const { Header, Content } = Layout;
const { Title } = Typography;

function AppContent() {
  const location = useLocation();
  const currentPath = location.pathname === '/' ? 'submit' : 'tasks';

  return (
    <Layout className="app-layout">
      <Header className="app-header">
        <div className="header-content">
          <Space align="center" className="logo-section">
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '8px',
              background: 'linear-gradient(135deg, var(--linear-accent-primary), var(--linear-accent-hover))',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '16px'
            }}>
              <RobotOutlined />
            </div>
            <Title level={3} className="logo-text">
              Auto In Scope
            </Title>
          </Space>
          <Menu 
            mode="horizontal" 
            selectedKeys={[currentPath]}
            className="nav-menu"
            items={[
              {
                key: 'submit',
                icon: <PlusOutlined style={{ fontSize: '14px' }} />,
                label: <Link to="/" style={{ fontSize: '14px' }}>New Analysis</Link>,
              },
              {
                key: 'tasks',
                icon: <TableOutlined style={{ fontSize: '14px' }} />,
                label: <Link to="/tasks" style={{ fontSize: '14px' }}>Tasks</Link>,
              },
            ]}
          />
        </div>
      </Header>
      <Content className="app-content">
        <div className="content-container">
          <Routes>
            <Route path="/" element={<TaskSubmissionForm />} />
            <Route path="/tasks" element={<TaskViewPage />} />
          </Routes>
        </div>
      </Content>
    </Layout>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;
