* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f8fafc;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Task Form Styling */
.task-form-container {
  max-width: 800px;
  margin: 0 auto;
}

.form-header, .view-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.form-title, .view-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.form-subtitle, .view-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.task-form-card, .task-view-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.task-form {
  padding: 8px;
}

.task-form .ant-form-item-label > label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.task-form .ant-select,
.task-form .ant-input,
.task-form .ant-input-affix-wrapper {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.task-form .ant-select:hover,
.task-form .ant-input:hover,
.task-form .ant-input-affix-wrapper:hover {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.task-form .ant-select-focused,
.task-form .ant-input:focus,
.task-form .ant-input-affix-wrapper:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.task-form .ant-input::placeholder {
  color: #9ca3af;
}

.task-form .ant-btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  height: 48px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 14px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.task-form .ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.submit-button {
  margin-top: 16px;
}

.submit-button:not(.ant-btn-loading):hover {
  animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
  0% { transform: translateY(-2px) scale(1); }
  50% { transform: translateY(-2px) scale(1.02); }
  100% { transform: translateY(-2px) scale(1); }
}

.task-form .ant-upload-btn {
  border-radius: 8px;
  border: 2px dashed #d1d5db;
  transition: all 0.3s ease;
}

.task-form .ant-upload-btn:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.task-form .ant-alert {
  border-radius: 8px;
  border: none;
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
}

/* Section Divider */
.section-divider {
  display: flex;
  align-items: center;
  margin: 32px 0 24px 0;
  gap: 16px;
}

.divider-line {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.divider-text {
  font-weight: 600;
  color: #4f46e5;
  font-size: 16px;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Enhanced TextArea */
.task-form .ant-input {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  line-height: 1.6;
}

.task-form textarea.ant-input {
  resize: vertical;
  min-height: 200px;
}

/* Task View Styling */
.task-view-container {
  max-width: 1200px;
  margin: 0 auto;
}

.task-view-card .ant-table {
  border-radius: 12px;
  overflow: hidden;
}

.task-view-card .ant-table-thead > tr > th {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
}

.task-view-card .ant-table-tbody > tr:hover > td {
  background: rgba(102, 126, 234, 0.05);
}

.task-view-card .ant-tag {
  border-radius: 20px;
  border: none;
  font-weight: 500;
  padding: 4px 12px;
}

.task-view-card .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.task-view-card .ant-btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.task-view-card .ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Modal Styling */
.ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.ant-modal-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-bottom: none;
}

.ant-modal-title {
  color: white;
  font-weight: 600;
}

.ant-modal-close {
  color: rgba(255, 255, 255, 0.8);
}

.ant-modal-close:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .task-form-container,
  .task-view-container {
    margin: 0 16px;
  }
  
  .form-title, .view-title {
    font-size: 28px;
  }
  
  .form-subtitle, .view-subtitle {
    font-size: 14px;
  }
  
  .task-form-card, .task-view-card {
    border-radius: 12px;
  }
}

/* Utility classes */
.min-h-screen {
  min-height: 100vh;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.text-blue-600 {
  color: #2563eb;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.p-3 {
  padding: 0.75rem;
}

.rounded {
  border-radius: 0.25rem;
}

.border {
  border-width: 1px;
}

.max-h-40 {
  max-height: 10rem;
}

.overflow-y-auto {
  overflow-y: auto;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}
