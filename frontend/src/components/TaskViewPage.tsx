import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Tag,
  Button,
  Space,
  Tooltip,
  Modal,
  Descriptions,
  Alert,
  message,
  Input,
} from 'antd';
import {
  EyeOutlined,
  CloudOutlined,
  ReloadOutlined,
  SearchOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { Task } from '../types';
import { apiService } from '../services/api';

const TaskViewPage: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [userIdFilter, setUserIdFilter] = useState('');

  useEffect(() => {
    loadTasks();
  }, [userIdFilter]);

  const loadTasks = async () => {
    setLoading(true);
    try {
      const taskList = await apiService.getTasks(userIdFilter);
      setTasks(taskList);
    } catch (error) {
      console.error('Failed to load tasks:', error);
      message.error('Failed to load tasks');
      // Mock data for development/demo
      setTasks([
        {
          task_id: 'ajassy_20250115_103000',
          user_id: 'ajassy',
          pt_pair: 'CAMERA_DIGITAL-FLASH_MEMORY',
          model: 'Claude 3.5 Sonnet V2',
          status: 'completed',
          created_at: '2025-01-15T10:30:00Z',
          completed_at: '2025-01-15T11:45:00Z',
          prompt_text: 'Analyze compatibility between digital cameras and flash memory cards...',
          result_file_url: 's3://results/task-001-result.xlsx',
        },
        {
          task_id: 'jane_smith_20250115_142000',
          user_id: 'jane_smith',
          pt_pair: 'VIDEO_GAME_CONSOLE-HEADPHONES',
          model: 'Claude 4 Sonnet',
          status: 'processing',
          created_at: '2025-01-15T14:20:00Z',
          prompt_text: 'Evaluate compatibility between video game consoles and headphones...',
        },
        {
          task_id: 'bob_wilson_20250115_091500',
          user_id: 'bob_wilson',
          pt_pair: 'PRINTER-INKJET_PRINTER_INK',
          model: 'Claude 3.5 Haiku',
          status: 'failed',
          created_at: '2025-01-15T09:15:00Z',
          prompt_text: 'Check compatibility between printers and inkjet printer ink...',
          error_message: 'Input file format error: Invalid data structure detected',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'processing';
      case 'failed':
        return 'error';
      case 'waiting':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'processing':
        return 'Processing';
      case 'failed':
        return 'Failed';
      case 'waiting':
        return 'Waiting';
      default:
        return status;
    }
  };

  const handleViewDetails = (task: Task) => {
    setSelectedTask(task);
    setModalVisible(true);
  };

  const handleOpenS3 = (task: Task) => {
    if (task.result_file_url) {
      // Convert S3 URI to AWS Console URL
      const s3Uri = task.result_file_url;
      let consoleUrl = '';
      
      try {
        // Parse S3 URI (e.g., s3://bucket-name/path/to/file)
        if (s3Uri.startsWith('s3://')) {
          const withoutProtocol = s3Uri.replace('s3://', '');
          const [bucket, ...pathParts] = withoutProtocol.split('/');
          const path = pathParts.join('/');
          
          // Generate AWS S3 Console URL
          consoleUrl = `https://s3.console.aws.amazon.com/s3/buckets/${bucket}?region=us-east-1&prefix=${encodeURIComponent(path)}`;
        } else {
          // If it's already an HTTP URL, use it directly
          consoleUrl = s3Uri;
        }
        
        window.open(consoleUrl, '_blank');
      } catch (error) {
        console.error('Failed to parse S3 URI:', error);
        message.error('Unable to open S3 link. Please check the file URL.');
      }
    } else {
      message.warning('No result file available');
    }
  };

  const filteredTasks = tasks.filter(task =>
    (task.task_id.toLowerCase().includes(searchText.toLowerCase()) ||
     task.pt_pair.toLowerCase().includes(searchText.toLowerCase()) ||
     task.model.toLowerCase().includes(searchText.toLowerCase()) ||
     task.status.toLowerCase().includes(searchText.toLowerCase()) ||
     task.user_id?.toLowerCase().includes(searchText.toLowerCase()))
  );

  const columns = [
    {
      title: 'User',
      dataIndex: 'user_id',
      key: 'user_id',
      width: 120,
      render: (text: string) => (
        <span style={{ fontWeight: '500', color: 'var(--linear-text-primary)' }}>{text}</span>
      ),
    },
    {
      title: 'Task ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 180,
      render: (text: string) => (
        <Tooltip title={text}>
          <span className="font-mono">{text.substring(0, 20)}...</span>
        </Tooltip>
      ),
    },
    {
      title: 'PT Pair',
      dataIndex: 'pt_pair',
      key: 'pt_pair',
      width: 250,
      render: (text: string) => (
        <Tooltip title={text}>
          <span className="text-blue-600">{text.replace(/_/g, ' ').replace(/-/g, ' → ')}</span>
        </Tooltip>
      ),
    },
    {
      title: 'Model',
      dataIndex: 'model',
      key: 'model',
      width: 150,
      render: (model: string) => (
        <span className="text-purple-600">{model}</span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: 'Completed',
      dataIndex: 'completed_at',
      key: 'completed_at',
      width: 150,
      render: (date: string) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_: any, record: Task) => (
        <Space size="small">
          <Button
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetails(record)}
          >
            Details
          </Button>
          {record.status === 'completed' && (record.result_file_url || (record.file_results && record.file_results.length > 0)) && (
            <Button
              icon={<CloudOutlined />}
              size="small"
              type="primary"
              onClick={() => {
                // Prefer file_results if available, otherwise use result_file_url
                if (record.file_results && record.file_results.length > 0) {
                  // Open the first result file or the directory
                  const s3Uri = record.file_results[0];
                  handleOpenS3({ ...record, result_file_url: s3Uri });
                } else {
                  handleOpenS3(record);
                }
              }}
              title="Open in AWS S3 Console"
            >
              View S3
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="task-view-container">
      <div className="view-header">
        <h1 className="view-title">Analysis Tasks</h1>
        <p className="view-subtitle">
          Track your compatibility analysis tasks, monitor progress, and download results when complete.
        </p>
      </div>
      
      {/* Search and Actions Bar */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px',
        padding: '16px 24px',
        background: 'var(--linear-bg-tertiary)',
        borderRadius: 'var(--linear-radius-lg)',
        border: '1px solid var(--linear-border-primary)'
      }}>
        <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          <Input
            size="large"
            placeholder="Filter by username..."
            prefix={<span style={{ color: 'var(--linear-text-tertiary)' }}>👤</span>}
            value={userIdFilter}
            onChange={(e) => setUserIdFilter(e.target.value)}
            style={{ width: '200px' }}
          />
          <Input
            size="large"
            placeholder="Search by task ID, product pair, model, or status..."
            prefix={<SearchOutlined style={{ color: 'var(--linear-text-tertiary)' }} />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '400px' }}
          />
        </div>
        <Button
          icon={<ReloadOutlined />}
          onClick={loadTasks}
          loading={loading}
          size="large"
          style={{ minWidth: '120px' }}
        >
          Refresh
        </Button>
      </div>
      
      <Card 
        className="task-view-card"
        styles={{ 
          body: { padding: 0 } 
        }}
      >
        <Table
          columns={columns}
          dataSource={filteredTasks}
          rowKey="task_id"
          loading={loading}
          size="large"
          scroll={{ x: 800 }}
          pagination={{
            pageSize: 15,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `Showing ${range[0]}-${range[1]} of ${total} tasks`,
            style: { 
              padding: '16px 24px',
              borderTop: '1px solid var(--linear-border-secondary)'
            }
          }}
          style={{ 
            borderRadius: 'var(--linear-radius-lg)',
            overflow: 'hidden'
          }}
        />
      </Card>

      <Modal
        title={
          <div style={{ 
            fontSize: '20px', 
            fontWeight: '600', 
            color: 'var(--linear-text-primary)' 
          }}>
            Task Details
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button 
            key="close" 
            size="large"
            onClick={() => setModalVisible(false)}
            style={{ minWidth: '100px' }}
          >
            Close
          </Button>,
          selectedTask?.status === 'completed' && (selectedTask?.result_file_url || (selectedTask?.file_results && selectedTask.file_results.length > 0)) && (
            <Button
              key="openS3"
              type="primary"
              size="large"
              icon={<ExportOutlined />}
              onClick={() => {
                // Prefer file_results if available, otherwise use result_file_url
                if (selectedTask.file_results && selectedTask.file_results.length > 0) {
                  const s3Uri = selectedTask.file_results[0];
                  handleOpenS3({ ...selectedTask, result_file_url: s3Uri });
                } else {
                  handleOpenS3(selectedTask);
                }
              }}
              style={{ minWidth: '140px' }}
              title="Open result files in AWS S3 Console"
            >
              Open S3 Results
            </Button>
          ),
        ]}
        width={900}
        styles={{
          body: { padding: '24px' },
          header: { 
            padding: '24px 24px 16px 24px',
            borderBottom: '1px solid var(--linear-border-primary)'
          }
        }}
      >
        {selectedTask && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
            <Descriptions 
              column={2}
              size="middle"
              style={{ 
                background: 'var(--linear-bg-secondary)',
                borderRadius: 'var(--linear-radius-lg)',
                border: '1px solid var(--linear-border-primary)'
              }}
            >
              <Descriptions.Item 
                label={<span style={{ fontWeight: '600' }}>User</span>}
              >
                <span style={{ fontWeight: '500', color: 'var(--linear-text-primary)' }}>
                  {selectedTask.user_id}
                </span>
              </Descriptions.Item>
              <Descriptions.Item 
                label={<span style={{ fontWeight: '600' }}>Task ID</span>}
              >
                <span className="font-mono" style={{ 
                  background: 'var(--linear-bg-tertiary)',
                  padding: '4px 8px',
                  borderRadius: 'var(--linear-radius-sm)',
                  border: '1px solid var(--linear-border-primary)',
                  fontSize: '12px'
                }}>
                  {selectedTask.task_id}
                </span>
              </Descriptions.Item>
              <Descriptions.Item 
                label={<span style={{ fontWeight: '600' }}>Product Type Pair</span>} 
                span={2}
              >
                <span style={{ fontWeight: '500' }}>
                  {selectedTask.pt_pair.replace(/_/g, ' ').replace(/-/g, ' → ')}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label={<span style={{ fontWeight: '600' }}>Status</span>}>
                <Tag color={getStatusColor(selectedTask.status)} style={{ fontWeight: '500' }}>
                  {getStatusText(selectedTask.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label={<span style={{ fontWeight: '600' }}>AI Model</span>}>
                <span className="text-purple-600" style={{ fontWeight: '500' }}>
                  {selectedTask.model}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label={<span style={{ fontWeight: '600' }}>Created</span>}>
                <span style={{ fontWeight: '500' }}>
                  {new Date(selectedTask.created_at).toLocaleString()}
                </span>
              </Descriptions.Item>
              {selectedTask.completed_at && (
                <Descriptions.Item label={<span style={{ fontWeight: '600' }}>Completed</span>} span={2}>
                  <span style={{ fontWeight: '500' }}>
                    {new Date(selectedTask.completed_at).toLocaleString()}
                  </span>
                </Descriptions.Item>
              )}
              {selectedTask.s3_uri && (
                <Descriptions.Item label={<span style={{ fontWeight: '600' }}>Data Source</span>} span={2}>
                  <span className="font-mono" style={{ 
                    background: 'var(--linear-bg-tertiary)',
                    padding: '4px 8px',
                    borderRadius: 'var(--linear-radius-sm)',
                    border: '1px solid var(--linear-border-primary)',
                    fontSize: '12px',
                    wordBreak: 'break-all'
                  }}>
                    {selectedTask.s3_uri}
                  </span>
                </Descriptions.Item>
              )}
              {/* Show file_results if available, otherwise show result_file_url */}
              {(selectedTask.file_results && selectedTask.file_results.length > 0) ? (
                <Descriptions.Item label={<span style={{ fontWeight: '600' }}>Result Files</span>} span={2}>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    {selectedTask.file_results.map((fileUrl, index) => {
                      const fileName = fileUrl.split('/').pop() || `File ${index + 1}`;
                      const isCSV = fileName.includes('claude_results.csv');
                      const isLog = fileName.endsWith('.log');
                      
                      return (
                        <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <span className="font-mono" style={{ 
                            background: 'var(--linear-bg-tertiary)',
                            padding: '4px 8px',
                            borderRadius: 'var(--linear-radius-sm)',
                            border: '1px solid var(--linear-border-primary)',
                            fontSize: '12px',
                            wordBreak: 'break-all',
                            flex: 1
                          }}>
                            {fileName}
                            {isCSV && <span style={{ color: 'var(--linear-accent-primary)', marginLeft: '8px' }}>📊 Results</span>}
                            {isLog && <span style={{ color: 'var(--linear-text-tertiary)', marginLeft: '8px' }}>📝 Log</span>}
                          </span>
                          <Button
                            size="small"
                            icon={<CloudOutlined />}
                            onClick={() => handleOpenS3({ ...selectedTask, result_file_url: fileUrl })}
                            title="Open in AWS S3 Console"
                            style={{ flexShrink: 0 }}
                          >
                            Open S3
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                </Descriptions.Item>
              ) : selectedTask.result_file_url && (
                <Descriptions.Item label={<span style={{ fontWeight: '600' }}>Result Location</span>} span={2}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span className="font-mono" style={{ 
                      background: 'var(--linear-bg-tertiary)',
                      padding: '4px 8px',
                      borderRadius: 'var(--linear-radius-sm)',
                      border: '1px solid var(--linear-border-primary)',
                      fontSize: '12px',
                      wordBreak: 'break-all',
                      flex: 1
                    }}>
                      {selectedTask.result_file_url}
                    </span>
                    <Button
                      size="small"
                      icon={<CloudOutlined />}
                      onClick={() => handleOpenS3(selectedTask)}
                      title="Open in AWS S3 Console"
                      style={{ flexShrink: 0 }}
                    >
                      Open S3
                    </Button>
                  </div>
                </Descriptions.Item>
              )}
            </Descriptions>

            {/* Prompt Template Section */}
            <div style={{ 
              background: 'var(--linear-bg-secondary)',
              borderRadius: 'var(--linear-radius-lg)',
              border: '1px solid var(--linear-border-primary)',
              overflow: 'hidden'
            }}>
              <div style={{ 
                padding: '16px 20px',
                borderBottom: '1px solid var(--linear-border-primary)',
                background: 'var(--linear-bg-tertiary)'
              }}>
                <h4 style={{ 
                  margin: 0,
                  fontSize: '16px',
                  fontWeight: '600',
                  color: 'var(--linear-text-primary)'
                }}>
                  Prompt Template
                </h4>
              </div>
              <div style={{ 
                padding: '16px 20px',
                maxHeight: '200px',
                overflowY: 'auto'
              }}>
                <pre style={{ 
                  margin: 0,
                  fontFamily: 'var(--linear-font-mono)',
                  fontSize: '13px',
                  lineHeight: '1.5',
                  color: 'var(--linear-text-secondary)',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word'
                }}>
                  {selectedTask.prompt_text}
                </pre>
              </div>
            </div>

            {selectedTask.status === 'failed' && selectedTask.error_message && (
              <Alert
                message="Analysis Failed"
                description={selectedTask.error_message}
                type="error"
                showIcon
              />
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TaskViewPage;