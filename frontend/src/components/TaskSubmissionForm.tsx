import React, { useState, useEffect } from 'react';
import {
  Form,
  Select,
  Input,
  Upload,
  Button,
  Card,
  Row,
  Col,
  message,
  Alert,
  Progress,
  Collapse,
  Typography,
  Tag,
  Space,
  Tooltip,
  Tabs,
} from 'antd';
import { UploadOutlined, CloudUploadOutlined, SendOutlined } from '@ant-design/icons';
import { PTair, TaskSubmission, PromptRequest, ModelOption } from '../types';
import { apiService } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;
const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const TaskSubmissionForm: React.FC = () => {
  const [form] = Form.useForm();
  const [ptPairs, setPtPairs] = useState<PTair[]>([]);
  const [promptText, setPromptText] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedS3Uri, setUploadedS3Uri] = useState<string>('');
  const [parsedPrompt, setParsedPrompt] = useState<any>(null);

  // Available models
  const availableModels: ModelOption[] = [
    { value: "Claude 3 Sonnet", label: "Claude 3 Sonnet - Balanced performance, good for general analysis" },
    { value: "Claude 3.5 Haiku", label: "Claude 3.5 Haiku - Fast and efficient, lightweight analysis (no images)" },
    { value: "Claude 3.5 Sonnet", label: "Claude 3.5 Sonnet - Enhanced reasoning, detailed compatibility analysis" },
    { value: "Claude 3.5 Sonnet V2", label: "Claude 3.5 Sonnet V2 - Improved accuracy, recommended for most tasks" },
    { value: "Claude 3.7 Sonnet", label: "Claude 3.7 Sonnet - Advanced model with superior reasoning capabilities" },
    { value: "Claude 4 Sonnet", label: "Claude 4 Sonnet - Latest model, highest accuracy and performance" },
  ];

  // PT Pairs from the original project
  const defaultPtPairs: PTair[] = [
    { value: "AIR_CONDITIONER-HVAC_AIR_FILTER", label: "Air Conditioner - HVAC Air Filter", primary_pt: "air_conditioner", accessory_pt: "hvac_air_filter" },
    { value: "CAMCORDER-CAMERA_ENCLOSURE", label: "Camcorder - Camera Enclosure", primary_pt: "camcorder", accessory_pt: "camera_enclosure" },
    { value: "CAMCORDER-CAMERA_LENS_FILTERS", label: "Camcorder - Camera Lens Filters", primary_pt: "camcorder", accessory_pt: "camera_lens_filters" },
    { value: "CAMCORDER-CAMERA_SUPPORT", label: "Camcorder - Camera Support", primary_pt: "camcorder", accessory_pt: "camera_support" },
    { value: "CAMCORDER-FLASH_MEMORY", label: "Camcorder - Flash Memory", primary_pt: "camcorder", accessory_pt: "flash_memory" },
    { value: "CAMERA_DIGITAL-CAMERA_ENCLOSURE", label: "Digital Camera - Camera Enclosure", primary_pt: "camera_digital", accessory_pt: "camera_enclosure" },
    { value: "CAMERA_DIGITAL-CAMERA_FLASH", label: "Digital Camera - Camera Flash", primary_pt: "camera_digital", accessory_pt: "camera_flash" },
    { value: "CAMERA_DIGITAL-CAMERA_LENS_FILTERS", label: "Digital Camera - Camera Lens Filters", primary_pt: "camera_digital", accessory_pt: "camera_lens_filters" },
    { value: "CAMERA_DIGITAL-FLASH_MEMORY", label: "Digital Camera - Flash Memory", primary_pt: "camera_digital", accessory_pt: "flash_memory" },
    { value: "CAMERA_FILM-PHOTOGRAPHIC_FILM", label: "Film Camera - Photographic Film", primary_pt: "camera_film", accessory_pt: "photographic_film" },
    { value: "CELLULAR_PHONE-FLASH_MEMORY", label: "Cellular Phone - Flash Memory", primary_pt: "cellular_phone", accessory_pt: "flash_memory" },
    { value: "CELLULAR_PHONE-HEADPHONES", label: "Cellular Phone - Headphones", primary_pt: "cellular_phone", accessory_pt: "headphones" },
    { value: "DIGITAL_VIDEO_RECORDER-COMPUTER_DRIVE_OR_STORAGE", label: "Digital Video Recorder - Computer Drive or Storage", primary_pt: "digital_video_recorder", accessory_pt: "computer_drive_or_storage" },
    { value: "GPS_OR_NAVIGATION_SYSTEM-FLASH_MEMORY", label: "GPS or Navigation System - Flash Memory", primary_pt: "gps_or_navigation_system", accessory_pt: "flash_memory" },
    { value: "NOTEBOOK_COMPUTER-COMPUTER_DRIVE_OR_STORAGE", label: "Notebook Computer - Computer Drive or Storage", primary_pt: "notebook_computer", accessory_pt: "computer_drive_or_storage" },
    { value: "NOTEBOOK_COMPUTER-FLASH_MEMORY", label: "Notebook Computer - Flash Memory", primary_pt: "notebook_computer", accessory_pt: "flash_memory" },
    { value: "PERSONAL_COMPUTER-COMPUTER_DRIVE_OR_STORAGE", label: "Personal Computer - Computer Drive or Storage", primary_pt: "personal_computer", accessory_pt: "computer_drive_or_storage" },
    { value: "PERSONAL_COMPUTER-FLASH_MEMORY", label: "Personal Computer - Flash Memory", primary_pt: "personal_computer", accessory_pt: "flash_memory" },
    { value: "PERSONAL_COMPUTER-INPUT_MOUSE", label: "Personal Computer - Input Mouse", primary_pt: "personal_computer", accessory_pt: "input_mouse" },
    { value: "PRINTER-INKJET_PRINTER_INK", label: "Printer - Inkjet Printer Ink", primary_pt: "printer", accessory_pt: "inkjet_printer_ink" },
    { value: "PRINTER-LASER_PRINTER_TONER", label: "Printer - Laser Printer Toner", primary_pt: "printer", accessory_pt: "laser_printer_toner" },
    { value: "SECURITY_CAMERA-FLASH_MEMORY", label: "Security Camera - Flash Memory", primary_pt: "security_camera", accessory_pt: "flash_memory" },
    { value: "TELEVISION-HEADPHONES", label: "Television - Headphones", primary_pt: "television", accessory_pt: "headphones" },
    { value: "VENT_HOOD-HVAC_AIR_FILTER", label: "Vent Hood - HVAC Air Filter", primary_pt: "vent_hood", accessory_pt: "hvac_air_filter" },
    { value: "VIDEO_GAME_CONSOLE-FLASH_MEMORY", label: "Video Game Console - Flash Memory", primary_pt: "video_game_console", accessory_pt: "flash_memory" },
    { value: "VIDEO_GAME_CONSOLE-HEADPHONES", label: "Video Game Console - Headphones", primary_pt: "video_game_console", accessory_pt: "headphones" }
  ];

  // Parse prompt text into structured sections - Enhanced to use API data
  const parsePromptText = (text: string, apiData?: any) => {
    if (!text && !apiData) return null;

    const sections: any = {};
    
    // If we have API data with structured compatibility criterion, use that
    if (apiData && apiData.compatibility_criterion) {
      const criterion = apiData.compatibility_criterion;
      
      // Primary product info from API data
      sections.primary = {
        name: criterion.primary_pt || 'Primary Product',
        definition: criterion.primary_definition || '',
        includes: criterion.primary_includes || [],
        excludes: criterion.primary_excludes || []
      };
      
      // Accessory product info from API data  
      sections.accessory = {
        name: criterion.accessory_pt || 'Accessory Product',
        definition: criterion.accessory_definition || '',
        includes: criterion.accessory_includes || [],
        excludes: criterion.accessory_excludes || []
      };
      
      return sections;
    }
    
    // Fallback: Parse from text (for backward compatibility)
    if (text) {
      // Extract Compatibility Standards - Fixed regex flags for ES2017 compatibility
      const compatibilityRegex = new RegExp('## Compatibility Standards([\\s\\S]*?)(?=##|$)');
      const compatibilityMatch = text.match(compatibilityRegex);
      
      if (compatibilityMatch) {
        const content = compatibilityMatch[1].trim();
        
        // Parse Primary Product (Given Product) - Fixed regex flags
        const primaryRegex = new RegExp('Given Product:\\s*Product:\\s*([^\\n]+)\\s*Definition:\\s*([^\\n]+)([\\s\\S]*?)(?=Target Product Category:|$)');
        const primaryMatch = content.match(primaryRegex);
        
        if (primaryMatch) {
          sections.primary = {
            name: primaryMatch[1].trim(),
            definition: primaryMatch[2].trim()
          };
          
          // Parse primary includes/excludes - Fixed regex flags
          const primaryIncludesRegex = new RegExp('Product Includes:([\\s\\S]*?)(?=Product Excludes:|Target Product Category:|$)');
          const primaryExcludesRegex = new RegExp('Product Excludes:([\\s\\S]*?)(?=Target Product Category:|$)');
          
          const primaryIncludesMatch = content.match(primaryIncludesRegex);
          const primaryExcludesMatch = content.match(primaryExcludesRegex);
          
          if (primaryIncludesMatch) {
            sections.primary.includes = primaryIncludesMatch[1].trim().split('•').filter(item => item.trim()).map(item => item.trim());
          }
          if (primaryExcludesMatch) {
            sections.primary.excludes = primaryExcludesMatch[1].trim().split('•').filter(item => item.trim()).map(item => item.trim());
          }
        }
        
        // Parse Accessory Product (Target Product Category) - Fixed regex flags
        const accessoryRegex = new RegExp('Target Product Category:\\s*([^\\n]+)([\\s\\S]*?)(?=Compatibility Criterion:|$)');
        const accessoryMatch = content.match(accessoryRegex);
        
        if (accessoryMatch) {
          sections.accessory = {
            name: accessoryMatch[1].trim()
          };
          
          const accessoryContent = accessoryMatch[2] || '';
          
          // Look for accessory definition
          const accessoryDefMatch = accessoryContent.match(/Definition:\s*([^\n]+)/);
          if (accessoryDefMatch) {
            sections.accessory.definition = accessoryDefMatch[1].trim();
          }
          
          // Parse accessory includes/excludes - Fixed regex flags
          const accessoryIncludesRegex = new RegExp('Target Product Includes:([\\s\\S]*?)(?=Target Product Excludes:|Compatibility Criterion:|$)');
          const accessoryExcludesRegex = new RegExp('Target Product Excludes:([\\s\\S]*?)(?=Compatibility Criterion:|$)');
          
          const accessoryIncludesMatch = accessoryContent.match(accessoryIncludesRegex);
          const accessoryExcludesMatch = accessoryContent.match(accessoryExcludesRegex);
          
          if (accessoryIncludesMatch) {
            sections.accessory.includes = accessoryIncludesMatch[1].trim().split('•').filter(item => item.trim()).map(item => item.trim());
          }
          if (accessoryExcludesMatch) {
            sections.accessory.excludes = accessoryExcludesMatch[1].trim().split('•').filter(item => item.trim()).map(item => item.trim());
          }
        }
      }
    }

    return sections;
  };

  useEffect(() => {
    // Try to load PT pairs from API, fallback to default if failed
    const loadPtPairs = async () => {
      try {
        const pairs = await apiService.getPTPairs();
        setPtPairs(pairs);
      } catch (error) {
        console.warn('Failed to load PT pairs from API, using defaults:', error);
        setPtPairs(defaultPtPairs);
      }
    };

    loadPtPairs();

    // Load saved login name from localStorage
    const savedLogin = localStorage.getItem('auto_in_scope_login');
    if (savedLogin) {
      form.setFieldsValue({ user_id: savedLogin });
    }
  }, [form]);

  const handlePtPairChange = async (value: string) => {
    setLoading(true);
    try {
      const request: PromptRequest = { pt_pair: value };
      const response = await apiService.getPrompt(request);
      setPromptText(response.prompt_template);
      // Use enhanced parsing with API data
      setParsedPrompt(parsePromptText(response.prompt_template, response));
      message.success('Prompt template loaded successfully');
    } catch (error) {
      console.error('Failed to load prompt:', error);
      message.error('Failed to load prompt template from server. Please try again or contact support if the issue persists.');
      // Set a default prompt if API fails
      const defaultText = `Default prompt template for ${value}. You can modify this prompt as needed.`;
      setPromptText(defaultText);
      setParsedPrompt(parsePromptText(defaultText));
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file: any) => {
    setUploading(true);
    setUploadProgress(0);
    
    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Upload file using presigned URL
      const result = await apiService.uploadFile(file);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      setUploadedS3Uri(result.s3_uri);
      
      message.success('File uploaded successfully! Your file is ready for analysis.');
      
      // File uploaded successfully
      
      setTimeout(() => {
        setUploadProgress(0);
        setUploading(false);
      }, 1000);
      
    } catch (error) {
      console.error('Upload failed:', error);
      
      // Extract detailed error message
      let errorMessage = 'File upload failed. Please try again.';
      if (error instanceof Error) {
        errorMessage = `Upload failed: ${error.message}`;
      } else if (typeof error === 'object' && error !== null) {
        if ('response' in error && (error as any).response?.data?.detail) {
          errorMessage = `Upload failed: ${(error as any).response.data.detail}`;
        }
      }
      
      message.error(errorMessage);
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      // Use uploaded S3 URI or manual S3 URI
      const s3Uri = uploadedS3Uri || values.s3_uri;
      
      if (!s3Uri) {
        message.error('Please upload a file or provide an S3 URI');
        return;
      }

      // Save login name to localStorage before submitting
      if (values.user_id) {
        localStorage.setItem('auto_in_scope_login', values.user_id);
      }

      const submission: TaskSubmission = {
        user_id: values.user_id,
        pt_pair: values.pt_pair,
        prompt_text: promptText,
        model: values.model,
        s3_uri: s3Uri,
      };

      // Show initial submission message
      message.loading({ content: 'Submitting your analysis task...', key: 'submit', duration: 0 });

      const result = await apiService.submitTask(submission);
      
      // Update message with success
      message.success({ 
        content: `Task submitted successfully! Your task ID is: ${result.task_id}. You can monitor progress in the Tasks page.`, 
        key: 'submit',
        duration: 8 
      });
      
      // Reset form but preserve login name
      const savedLogin = values.user_id;
      form.resetFields();
      form.setFieldsValue({ user_id: savedLogin });
      setPromptText('');
      setUploadedS3Uri('');
      setParsedPrompt(null);
      
    } catch (error) {
      console.error('Failed to submit task:', error);
      
      // Extract error message for better user feedback
      let errorMessage = 'Failed to submit task. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        if ('response' in error && (error as any).response?.data?.detail) {
          errorMessage = (error as any).response.data.detail;
        } else if ('message' in error) {
          errorMessage = (error as any).message;
        }
      }
      
      message.error({ 
        content: `Submission failed: ${errorMessage}`, 
        key: 'submit',
        duration: 6 
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle login name input change to save to localStorage
  const handleLoginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value) {
      localStorage.setItem('auto_in_scope_login', value);
    }
  };

  const uploadProps = {
    beforeUpload: (file: any) => {
      // Allow Excel, CSV, TSV files
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv', // .csv
        'text/tab-separated-values' // .tsv
      ];
      
      const allowedExtensions = ['.xlsx', '.xls', '.csv', '.tsv'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
      
      const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);
      
      if (!isValidType) {
        message.error('Only Excel (.xlsx, .xls), CSV, and TSV files are allowed!');
        return false;
      }
      
      // Check file size (limit to 100MB)
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('File must be smaller than 100MB!');
        return false;
      }

      // Start upload process
      handleFileUpload(file);
      return false; // Prevent auto upload
    },
    fileList: [],
    showUploadList: false,
  };

  return (
    <div className="task-form-container">
      <div className="form-header">
        <h1 className="form-title">Submit In-Scope Task</h1>
        <p className="form-subtitle">
          Analyze In-Scope using advanced Claude AI models. Configure your analysis parameters and upload your data to get started.
        </p>
      </div>
      
      <Card className="task-form-card">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="task-form"
        >
          {/* User Authentication Section */}
          <div className="login-section">
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Form.Item
                  label="Login (Username)"
                  name="user_id"
                  rules={[
                    { required: true, message: 'Please enter your username' },
                    { min: 2, message: 'Username must be at least 2 characters' },
                    { max: 50, message: 'Username must be less than 50 characters' },
                    { pattern: /^[a-zA-Z0-9_-]+$/, message: 'Username can only contain letters, numbers, underscores, and hyphens' }
                  ]}
                  extra="Your username will be used to organize and filter your tasks"
                >
                  <Input
                    size="large"
                    placeholder="Enter your username (e.g., ajassy)"
                    prefix={<span style={{ color: 'var(--linear-text-tertiary)' }}>👤</span>}
                    onChange={handleLoginChange}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* Analysis Configuration Section */}
          <div style={{ marginBottom: '32px' }}>
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Form.Item
                  label="Product Type Pair"
                  name="pt_pair"
                  rules={[{ required: true, message: 'Please select a product type pair' }]}
                >
                  <Select
                    size="large"
                    placeholder="Choose the product compatibility pair to analyze"
                    loading={loading}
                    onChange={handlePtPairChange}
                    showSearch
                    filterOption={(input, option) =>
                      option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                    }
                  >
                    {ptPairs.map((pair) => (
                      <Option key={pair.value} value={pair.value}>
                        {pair.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              
              <Col span={24}>
                <Form.Item
                  label="Claude Model"
                  name="model"
                  rules={[{ required: true, message: 'Please select an AI model' }]}
                  extra="Choose based on your analysis needs. Haiku is fastest but text-only. Sonnet V2 is recommended for most tasks. Claude 4 provides highest accuracy."
                >
                  <Select
                    size="large"
                    placeholder="Select a Claude model for analysis"
                    showSearch
                    filterOption={(input, option) =>
                      option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                    }
                  >
                    {availableModels.map((model) => (
                      <Option key={model.value} value={model.value}>
                        {model.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* Prompt Configuration Section */}
          <div style={{ marginBottom: '32px' }}>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item 
                  label={
                    <Space>
                      <span>Prompt Template</span>
                      <Tooltip title="This template defines how Claude AI will analyze your products for compatibility">
                        <Tag color="blue" style={{ fontSize: '11px', marginLeft: '4px' }}>Auto-Generated</Tag>
                      </Tooltip>
                    </Space>
                  }
                  extra="This template will be automatically loaded based on your product type pair selection. You can review and customize it as needed."
                >
                  {parsedPrompt ? (
                    // Structured Prompt Display
                    <Card 
                      size="small" 
                      style={{ 
                        backgroundColor: '#fafbfc', 
                        border: '1px solid var(--linear-border-primary)',
                        marginBottom: '16px'
                      }}
                    >
                      <Collapse 
                        defaultActiveKey={['standards']} 
                        ghost
                        size="small"
                        style={{ background: 'transparent' }}
                      >
                        {/* Compatibility Standards */}
                        {(parsedPrompt.primary || parsedPrompt.accessory) && (
                          <Panel 
                            header={
                              <Space>
                                <Tag color="blue">Standards</Tag>
                                <Text strong>Compatibility Standards</Text>
                              </Space>
                            } 
                            key="standards"
                          >
                            <Tabs defaultActiveKey="primary" type="card" size="small">
                              {/* Primary Product Tab */}
                              {parsedPrompt.primary && (
                                <TabPane 
                                  tab={
                                    <Space>
                                      <Tag color="purple">Primary</Tag>
                                      {parsedPrompt.primary.name}
                                    </Space>
                                  } 
                                  key="primary"
                                >
                                  <Card size="small" style={{ backgroundColor: '#f8f9fa' }}>
                                    <Title level={5} style={{ margin: '0 0 12px 0', fontSize: '14px' }}>
                                      {parsedPrompt.primary.name}
                                    </Title>
                                    
                                    {parsedPrompt.primary.definition && (
                                      <div style={{ marginBottom: '12px' }}>
                                        <Text strong style={{ fontSize: '13px', color: '#1890ff' }}>Definition:</Text>
                                        <Paragraph style={{ fontSize: '12px', marginTop: '4px', marginBottom: 0 }}>
                                          {parsedPrompt.primary.definition}
                                        </Paragraph>
                                      </div>
                                    )}
                                    
                                    {parsedPrompt.primary.includes && (
                                      <div style={{ marginBottom: '12px' }}>
                                        <Text strong style={{ fontSize: '13px', color: '#52c41a' }}>Includes:</Text>
                                        <ul style={{ fontSize: '12px', margin: '4px 0 0 16px', padding: 0 }}>
                                          {parsedPrompt.primary.includes.map((item: string, index: number) => (
                                            <li key={index} style={{ marginBottom: '4px' }}>{item}</li>
                                          ))}
                                        </ul>
                                      </div>
                                    )}
                                    
                                    {parsedPrompt.primary.excludes && (
                                      <div>
                                        <Text strong style={{ fontSize: '13px', color: '#ff4d4f' }}>Excludes:</Text>
                                        <ul style={{ fontSize: '12px', margin: '4px 0 0 16px', padding: 0 }}>
                                          {parsedPrompt.primary.excludes.map((item: string, index: number) => (
                                            <li key={index} style={{ marginBottom: '4px' }}>{item}</li>
                                          ))}
                                        </ul>
                                      </div>
                                    )}
                                  </Card>
                                </TabPane>
                              )}
                              
                              {/* Accessory Product Tab */}
                              {parsedPrompt.accessory && (
                                <TabPane 
                                  tab={
                                    <Space>
                                      <Tag color="orange">Accessory</Tag>
                                      {parsedPrompt.accessory.name}
                                    </Space>
                                  } 
                                  key="accessory"
                                >
                                  <Card size="small" style={{ backgroundColor: '#f8f9fa' }}>
                                    <Title level={5} style={{ margin: '0 0 12px 0', fontSize: '14px' }}>
                                      {parsedPrompt.accessory.name}
                                    </Title>
                                    
                                    {parsedPrompt.accessory.definition && (
                                      <div style={{ marginBottom: '12px' }}>
                                        <Text strong style={{ fontSize: '13px', color: '#1890ff' }}>Definition:</Text>
                                        <Paragraph style={{ fontSize: '12px', marginTop: '4px', marginBottom: 0 }}>
                                          {parsedPrompt.accessory.definition}
                                        </Paragraph>
                                      </div>
                                    )}
                                    
                                    {parsedPrompt.accessory.includes && (
                                      <div style={{ marginBottom: '12px' }}>
                                        <Text strong style={{ fontSize: '13px', color: '#52c41a' }}>Includes:</Text>
                                        <ul style={{ fontSize: '12px', margin: '4px 0 0 16px', padding: 0 }}>
                                          {parsedPrompt.accessory.includes.map((item: string, index: number) => (
                                            <li key={index} style={{ marginBottom: '4px' }}>{item}</li>
                                          ))}
                                        </ul>
                                      </div>
                                    )}
                                    
                                    {parsedPrompt.accessory.excludes && (
                                      <div>
                                        <Text strong style={{ fontSize: '13px', color: '#ff4d4f' }}>Excludes:</Text>
                                        <ul style={{ fontSize: '12px', margin: '4px 0 0 16px', padding: 0 }}>
                                          {parsedPrompt.accessory.excludes.map((item: string, index: number) => (
                                            <li key={index} style={{ marginBottom: '4px' }}>{item}</li>
                                          ))}
                                        </ul>
                                      </div>
                                    )}
                                  </Card>
                                </TabPane>
                              )}
                            </Tabs>
                          </Panel>
                        )}

                        {/* Raw Template Editor */}
                        <Panel 
                          header={
                            <Space>
                              <Tag color="default">Edit</Tag>
                              <Text strong>Raw Template (Advanced)</Text>
                            </Space>
                          } 
                          key="raw"
                        >
                          <TextArea
                            value={promptText}
                            onChange={(e) => {
                              setPromptText(e.target.value);
                              setParsedPrompt(parsePromptText(e.target.value));
                            }}
                            rows={8}
                            style={{ 
                              fontFamily: 'var(--linear-font-mono)', 
                              fontSize: '11px',
                              backgroundColor: '#fafafa'
                            }}
                          />
                        </Panel>
                      </Collapse>
                    </Card>
                  ) : (
                    // Fallback Simple Display
                    <TextArea
                      value={promptText}
                      onChange={(e) => setPromptText(e.target.value)}
                      rows={6}
                      placeholder="Select a product type pair above to load the prompt template..."
                      disabled={loading}
                      style={{ fontFamily: 'var(--linear-font-mono)', fontSize: '13px' }}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
          </div>

          <div className="section-divider">
            <div className="divider-line"></div>
            <span className="divider-text">Data Upload</span>
            <div className="divider-line"></div>
          </div>
          
          <Alert
            message="Upload your analysis data"
            description="Upload a data file (Excel, CSV, or TSV format, max 100MB) or provide an S3 URI if your file is already stored in AWS S3. Only one option is required."
            type="info"
            showIcon
            style={{ marginBottom: '24px' }}
          />

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item 
                label="Upload File"
                extra="Supported formats: .xlsx, .xls, .csv, .tsv"
              >
                <Upload {...uploadProps} disabled={uploading}>
                  <Button 
                    size="large"
                    icon={<UploadOutlined />} 
                    loading={uploading}
                    disabled={uploading}
                    style={{ width: '100%', height: '48px' }}
                  >
                    {uploading ? 'Uploading...' : 'Choose File'}
                  </Button>
              </Upload>
                {uploading && (
                  <div style={{ marginTop: '12px' }}>
                    <Progress 
                      percent={uploadProgress} 
                      size="small"
                      strokeColor="var(--linear-accent-primary)"
                    />
                  </div>
                )}
                {uploadedS3Uri && (
                  <Alert
                    message="File uploaded successfully!"
                    description={`File location: ${uploadedS3Uri}`}
                    type="success"
                    showIcon
                    style={{ marginTop: '12px' }}
                    closable
                    onClose={() => setUploadedS3Uri('')}
                  />
                )}
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                label="S3 URI (Alternative)"
                name="s3_uri"
                extra="Provide an S3 URI if your file is already uploaded to AWS S3"
              >
                <Input
                  size="large"
                  prefix={<CloudUploadOutlined style={{ color: 'var(--linear-text-tertiary)' }} />}
                  placeholder="s3://your-bucket/path/to/file.xlsx"
                  disabled={!!uploadedS3Uri}
                  value={uploadedS3Uri ? uploadedS3Uri : undefined}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* Submit Section */}
          <div style={{ marginTop: '32px', paddingTop: '24px', borderTop: '1px solid var(--linear-border-secondary)' }}>
            <Row>
              <Col span={24}>
                <Form.Item style={{ marginBottom: 0 }}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={submitting}
                    icon={<SendOutlined />}
                    size="large"
                    block
                    className="submit-button"
                    disabled={uploading}
                    style={{ height: '56px', fontSize: '16px', fontWeight: '600' }}
                  >
                    {submitting ? 'Submitting Analysis...' : 'Start Analysis'}
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default TaskSubmissionForm;