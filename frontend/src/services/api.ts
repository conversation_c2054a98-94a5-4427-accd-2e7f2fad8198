import axios from 'axios';
import { PTair, TaskSubmission, Task, PromptRequest, PromptResponse } from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interface for presigned URL request
interface PresignedUrlRequest {
  filename: string;
  content_type: string;
  file_size: number;
}

// Interface for presigned URL response
interface PresignedUrlResponse {
  presigned_url: string;
  s3_uri: string;
  expires_in: number;
}

export const apiService = {
  // Get list of available PT pairs
  getPTPairs: async (): Promise<PTair[]> => {
    const response = await api.get('/pt-pairs');
    return response.data;
  },

  // Get prompt template for selected PT pair
  getPrompt: async (request: PromptRequest): Promise<PromptResponse> => {
    const response = await api.post('/prompt', request);
    return response.data;
  },

  // Get presigned URL for direct S3 upload
  getPresignedUrl: async (file: File): Promise<PresignedUrlResponse> => {
    const request: PresignedUrlRequest = {
      filename: file.name,
      content_type: file.type || 'application/octet-stream',
      file_size: file.size
    };
    
    const response = await api.post('/upload/presigned-url', request);
    return response.data;
  },

  // Upload file directly to S3 using presigned URL
  uploadToS3: async (file: File, presignedUrl: string): Promise<void> => {
    await axios.put(presignedUrl, file, {
      headers: {
        'Content-Type': file.type || 'application/octet-stream',
        'Content-Length': file.size.toString(),
      },
    });
  },

  // Submit new analysis task
  submitTask: async (task: TaskSubmission): Promise<{ task_id: string }> => {
    // Now using JSON request instead of FormData
    const response = await api.post('/tasks', {
      user_id: task.user_id,
      pt_pair: task.pt_pair,
      prompt_text: task.prompt_text,
      model: task.model,
      s3_uri: task.s3_uri
    });
    return response.data;
  },

  // Complete file upload flow: get presigned URL, upload to S3, return S3 URI
  uploadFile: async (file: File): Promise<{ s3_uri: string }> => {
    try {
      // Step 1: Get presigned URL
      const presignedData = await apiService.getPresignedUrl(file);
      
      // Step 2: Upload file to S3
      await apiService.uploadToS3(file, presignedData.presigned_url);
      
      // Step 3: Return S3 URI
      return { s3_uri: presignedData.s3_uri };
    } catch (error) {
      console.error('File upload failed:', error);
      throw new Error('Failed to upload file to S3');
    }
  },

  // Get user's task list
  getTasks: async (userId?: string): Promise<Task[]> => {
    const params = userId ? { user_id: userId } : {};
    const response = await api.get('/tasks', { params });
    return response.data;
  },

  // Get specific task details
  getTask: async (taskId: string): Promise<Task> => {
    const response = await api.get(`/tasks/${taskId}`);
    return response.data;
  },
};