export interface PTair {
  value: string;
  label: string;
  primary_pt: string;
  accessory_pt: string;
}

export interface TaskSubmission {
  user_id: string;
  pt_pair: string;
  prompt_text: string;
  model: string;
  file?: File;
  s3_uri?: string;
}

export interface Task {
  task_id: string;
  user_id: string;
  pt_pair: string;
  prompt_text: string;
  model: string;
  file_path?: string;
  s3_uri?: string;
  status: 'waiting' | 'processing' | 'completed' | 'failed';
  created_at: string;
  updated_at?: string;
  completed_at?: string;
  result_file_url?: string;
  file_results?: string[]; // New field for multiple result file URLs
  error_message?: string;
  progress?: number;
  total_items?: number;
  processed_items?: number;
}

export interface PromptRequest {
  pt_pair: string;
}

export interface PromptResponse {
  prompt_template: string;
  compatibility_criterion: any;
}

export interface ModelOption {
  value: string;
  label: string;
}