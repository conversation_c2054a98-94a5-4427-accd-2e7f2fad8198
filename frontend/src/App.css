/* Linear Design System - Auto In Scope */

/* CSS Variables - Linear Color Palette */
:root {
  /* Core Colors */
  --linear-bg-primary: #fcfcfd;
  --linear-bg-secondary: #f8f9fa;
  --linear-bg-tertiary: #ffffff;
  --linear-bg-elevated: #ffffff;
  
  /* Surface Colors */
  --linear-surface-overlay: rgba(255, 255, 255, 0.8);
  --linear-surface-glass: rgba(255, 255, 255, 0.9);
  
  /* Border Colors */
  --linear-border-primary: #e1e5e9;
  --linear-border-secondary: #f1f3f5;
  --linear-border-focus: #5e6ad2;
  --linear-border-hover: #d0d6db;
  
  /* Text Colors */
  --linear-text-primary: #0d1117;
  --linear-text-secondary: #656d76;
  --linear-text-tertiary: #8b949e;
  --linear-text-disabled: #afb8c1;
  
  /* Accent Colors */
  --linear-accent-primary: #5e6ad2;
  --linear-accent-hover: #4c5bc7;
  --linear-accent-pressed: #3d4cbc;
  --linear-accent-subtle: #f0f1ff;
  
  /* Status Colors */
  --linear-success: #00c896;
  --linear-success-bg: #ecfdf5;
  --linear-warning: #f59e0b;
  --linear-warning-bg: #fffbeb;
  --linear-error: #ef4444;
  --linear-error-bg: #fef2f2;
  --linear-info: #3b82f6;
  --linear-info-bg: #eff6ff;
  
  /* Shadows */
  --linear-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --linear-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --linear-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --linear-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Radius */
  --linear-radius-sm: 6px;
  --linear-radius-md: 8px;
  --linear-radius-lg: 12px;
  --linear-radius-xl: 16px;
  
  /* Spacing */
  --linear-space-xs: 4px;
  --linear-space-sm: 8px;
  --linear-space-md: 16px;
  --linear-space-lg: 24px;
  --linear-space-xl: 32px;
  --linear-space-2xl: 48px;
  
  /* Typography */
  --linear-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --linear-font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* Global Resets */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--linear-font-family);
  color: var(--linear-text-primary);
  background: var(--linear-bg-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Override Ant Design styles with Linear aesthetics */
.ant-layout {
  background: var(--linear-bg-primary) !important;
}

.ant-card {
  border: 1px solid var(--linear-border-primary) !important;
  border-radius: var(--linear-radius-lg) !important;
  box-shadow: var(--linear-shadow-sm) !important;
  background: var(--linear-bg-tertiary) !important;
}

.ant-card:hover {
  border-color: var(--linear-border-hover) !important;
  box-shadow: var(--linear-shadow-md) !important;
}

.ant-form-item-label > label {
  color: var(--linear-text-primary) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.ant-form-item-extra {
  color: var(--linear-text-secondary) !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  margin-top: 4px !important;
}

.ant-input,
.ant-select-selector,
.ant-input-affix-wrapper {
  border: 1px solid var(--linear-border-primary) !important;
  border-radius: var(--linear-radius-md) !important;
  background: var(--linear-bg-tertiary) !important;
  transition: all 0.2s ease !important;
}

.ant-input:hover,
.ant-select-selector:hover,
.ant-input-affix-wrapper:hover {
  border-color: var(--linear-border-hover) !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-input-affix-wrapper:focus {
  border-color: var(--linear-accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(94, 106, 210, 0.1) !important;
}

.ant-btn {
  border-radius: var(--linear-radius-md) !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  height: 40px !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: var(--linear-space-sm) !important;
}

.ant-btn-primary {
  background: var(--linear-accent-primary) !important;
  border-color: var(--linear-accent-primary) !important;
  color: white !important;
}

.ant-btn-primary:hover {
  background: var(--linear-accent-hover) !important;
  border-color: var(--linear-accent-hover) !important;
}

.ant-btn-primary:active {
  background: var(--linear-accent-pressed) !important;
  border-color: var(--linear-accent-pressed) !important;
}

.ant-btn-default {
  background: var(--linear-bg-tertiary) !important;
  border-color: var(--linear-border-primary) !important;
  color: var(--linear-text-primary) !important;
}

.ant-btn-default:hover {
  border-color: var(--linear-border-hover) !important;
  background: var(--linear-bg-secondary) !important;
}

.ant-tag {
  border-radius: var(--linear-radius-sm) !important;
  border: none !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 2px 8px !important;
}

.ant-table {
  background: var(--linear-bg-tertiary) !important;
  border-radius: var(--linear-radius-lg) !important;
  border: 1px solid var(--linear-border-primary) !important;
}

.ant-table-thead > tr > th {
  background: var(--linear-bg-secondary) !important;
  border-bottom: 1px solid var(--linear-border-primary) !important;
  color: var(--linear-text-secondary) !important;
  font-weight: 600 !important;
  font-size: 13px !important;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid var(--linear-border-secondary) !important;
  color: var(--linear-text-primary) !important;
}

.ant-table-tbody > tr:hover > td {
  background: var(--linear-bg-secondary) !important;
}

.ant-modal-content {
  border-radius: var(--linear-radius-xl) !important;
  box-shadow: var(--linear-shadow-xl) !important;
  border: 1px solid var(--linear-border-primary) !important;
}

.ant-modal-header {
  border-bottom: 1px solid var(--linear-border-primary) !important;
  background: var(--linear-bg-secondary) !important;
  border-radius: var(--linear-radius-xl) var(--linear-radius-xl) 0 0 !important;
}

.ant-modal-title {
  color: var(--linear-text-primary) !important;
  font-weight: 600 !important;
}

/* App Layout Styles */
.app-layout {
  min-height: 100vh;
  background: var(--linear-bg-primary);
}

.app-header {
  background: var(--linear-surface-glass);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid var(--linear-border-secondary);
  box-shadow: var(--linear-shadow-sm);
  padding: 0;
  height: 64px;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--linear-space-lg);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--linear-space-md);
}

.logo-icon {
  font-size: 24px;
  color: var(--linear-accent-primary);
}

.logo-text {
  margin: 0 !important;
  color: var(--linear-text-primary) !important;
  font-weight: 600 !important;
  font-size: 20px !important;
}

.nav-menu {
  border: none !important;
  background: transparent !important;
  display: flex !important;
  gap: var(--linear-space-sm) !important;
}

.nav-menu .ant-menu-item {
  border-radius: var(--linear-radius-md) !important;
  margin: 0 !important;
  transition: all 0.2s ease !important;
  background: transparent !important;
  color: var(--linear-text-secondary) !important;
  border: none !important;
  padding: var(--linear-space-sm) var(--linear-space-md) !important;
  height: 36px !important;
  line-height: 20px !important;
}

.nav-menu .ant-menu-item:hover {
  background: var(--linear-bg-secondary) !important;
  color: var(--linear-text-primary) !important;
}

.nav-menu .ant-menu-item-selected {
  background: var(--linear-accent-subtle) !important;
  color: var(--linear-accent-primary) !important;
}

.nav-menu .ant-menu-item::after {
  display: none !important;
}

.nav-menu .ant-menu-item a {
  color: inherit !important;
  text-decoration: none !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.app-content {
  padding: 0;
  min-height: calc(100vh - 64px);
  background: var(--linear-bg-primary);
}

.content-container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--linear-space-2xl) var(--linear-space-lg);
}

/* Task Form Styles */
.task-form-container {
  max-width: 800px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 20px !important; /* was 48px (var(--linear-space-2xl)), now 20px */
}

.login-section {
  margin-bottom: 32px !important;
  margin-top: 4px !important; /* Further reduced for tighter spacing */
}

.login-section .ant-form-item {
  margin-bottom: 24px !important; /* Reduce bottom margin of the form item */
}

.login-section .ant-form-item-extra {
  font-size: 10px !important; /* Further reduced font size */
  line-height: 1.2 !important;
  margin-top: 2px !important;
  color: var(--linear-text-secondary) !important;
  opacity: 0.8 !important; /* Make it more subtle */
}

.form-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--linear-text-primary);
  margin-bottom: var(--linear-space-md);
  line-height: 1.2;
}

.form-subtitle {
  font-size: 16px;
  color: var(--linear-text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.5;
}

.task-form-card {
  background: var(--linear-bg-tertiary);
  border: 1px solid var(--linear-border-primary);
  border-radius: var(--linear-radius-xl);
  box-shadow: var(--linear-shadow-md);
  padding: var(--linear-space-2xl);
}

.task-form .ant-form-item {
  margin-bottom: var(--linear-space-xl);
}

.section-divider {
  display: flex;
  align-items: center;
  margin: var(--linear-space-2xl) 0;
  gap: var(--linear-space-md);
}

.divider-line {
  flex: 1;
  height: 1px;
  background: var(--linear-border-primary);
}

.divider-text {
  color: var(--linear-text-secondary);
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
}

.submit-button {
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  border-radius: var(--linear-radius-lg) !important;
}

/* Task View Styles */
.task-view-container {
  max-width: 1200px;
  margin: 0 auto;
}

.view-header {
  margin-bottom: var(--linear-space-2xl);
}

.view-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--linear-text-primary);
  margin-bottom: var(--linear-space-md);
  line-height: 1.2;
}

.view-subtitle {
  font-size: 16px;
  color: var(--linear-text-secondary);
  line-height: 1.5;
}

.task-view-card {
  background: var(--linear-bg-tertiary);
  border: 1px solid var(--linear-border-primary);
  border-radius: var(--linear-radius-xl);
  box-shadow: var(--linear-shadow-md);
}

.task-view-card .ant-card-head {
  border-bottom: 1px solid var(--linear-border-primary);
  background: var(--linear-bg-secondary);
  border-radius: var(--linear-radius-xl) var(--linear-radius-xl) 0 0;
}

.task-view-card .ant-card-body {
  padding: 0;
}

/* Status Colors */
.ant-tag-success {
  background: var(--linear-success-bg) !important;
  color: var(--linear-success) !important;
}

.ant-tag-processing {
  background: var(--linear-info-bg) !important;
  color: var(--linear-info) !important;
}

.ant-tag-error {
  background: var(--linear-error-bg) !important;
  color: var(--linear-error) !important;
}

.ant-tag-default {
  background: var(--linear-bg-secondary) !important;
  color: var(--linear-text-secondary) !important;
}

/* Utility Classes */
.text-purple-600 {
  color: var(--linear-accent-primary) !important;
}

.text-blue-600 {
  color: var(--linear-info) !important;
}

.font-mono {
  font-family: var(--linear-font-mono) !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-container {
    padding: var(--linear-space-xl) var(--linear-space-md);
  }
  
  .header-content {
    padding: 0 var(--linear-space-md);
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: var(--linear-space-md);
    gap: var(--linear-space-md);
  }
  
  .app-header {
    height: auto;
    position: relative;
  }
  
  .form-title,
  .view-title {
    font-size: 28px;
  }
  
  .content-container {
    padding: var(--linear-space-lg) var(--linear-space-md);
  }
  
  .task-form-card {
    padding: var(--linear-space-lg);
  }
  
  .nav-menu {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .form-title,
  .view-title {
    font-size: 24px;
  }
  
  .logo-text {
    font-size: 18px !important;
  }
  
  .nav-menu .ant-menu-item {
    padding: var(--linear-space-xs) var(--linear-space-md) !important;
  }
  
  .content-container {
    padding: var(--linear-space-md);
  }
  
  .task-form-card {
    padding: var(--linear-space-md);
  }
}

/* Animation and Transitions */
.ant-card,
.ant-btn,
.ant-input,
.ant-select-selector {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Loading and Progress States */
.ant-progress-text {
  color: var(--linear-text-secondary) !important;
  font-size: 12px !important;
}

.ant-progress-bg {
  background: var(--linear-bg-secondary) !important;
}

.ant-progress-inner .ant-progress-bg {
  background: var(--linear-accent-primary) !important;
}

/* Notifications and Alerts */
.ant-alert {
  border-radius: var(--linear-radius-md) !important;
  border: 1px solid var(--linear-border-primary) !important;
}

.ant-alert-info {
  background: var(--linear-info-bg) !important;
  border-color: var(--linear-info) !important;
}

.ant-alert-success {
  background: var(--linear-success-bg) !important;
  border-color: var(--linear-success) !important;
}

.ant-alert-warning {
  background: var(--linear-warning-bg) !important;
  border-color: var(--linear-warning) !important;
}

.ant-alert-error {
  background: var(--linear-error-bg) !important;
  border-color: var(--linear-error) !important;
}