#!/bin/bash

# AWS Amplify Deployment Script for In Scope Automation Tool
# This script follows AWS best practices for deployment

set -e  # Exit on any error

echo "🚀 Starting AWS Amplify Deployment Process..."
echo "================================================"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="in-scope-automation-tool"
REGION="us-east-1"
REPOSITORY_NAME="in-scope-frontend"

# Step 1: Check AWS CLI configuration
echo -e "${BLUE}📋 Step 1: Checking AWS CLI configuration...${NC}"
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    echo -e "${RED}❌ AWS CLI not configured. Please run 'aws configure' first.${NC}"
    echo -e "${YELLOW}💡 You'll need:${NC}"
    echo "   - AWS Access Key ID"
    echo "   - AWS Secret Access Key" 
    echo "   - Default region (recommend: us-east-1)"
    echo "   - Default output format (recommend: json)"
    exit 1
else
    echo -e "${GREEN}✅ AWS CLI configured successfully${NC}"
    aws sts get-caller-identity
fi

# Step 2: Create CodeCommit repository
echo -e "${BLUE}📦 Step 2: Creating CodeCommit repository...${NC}"
if aws codecommit get-repository --repository-name "$REPOSITORY_NAME" --region "$REGION" >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Repository '$REPOSITORY_NAME' already exists${NC}"
else
    echo "Creating new CodeCommit repository..."
    aws codecommit create-repository \
        --repository-name "$REPOSITORY_NAME" \
        --repository-description "Frontend for In Scope Automation Tool - React TypeScript application with modern UI" \
        --region "$REGION"
    echo -e "${GREEN}✅ Repository created successfully${NC}"
fi

# Get repository clone URL
CLONE_URL=$(aws codecommit get-repository \
    --repository-name "$REPOSITORY_NAME" \
    --region "$REGION" \
    --query 'repositoryMetadata.cloneUrlHttp' \
    --output text)

echo -e "${GREEN}📍 Repository URL: $CLONE_URL${NC}"

# Step 3: Configure Git credentials helper for CodeCommit
echo -e "${BLUE}🔐 Step 3: Configuring Git credentials...${NC}"
git config credential.helper '!aws codecommit credential-helper $@'
git config credential.UseHttpPath true
echo -e "${GREEN}✅ Git credentials configured for CodeCommit${NC}"

# Step 4: Add CodeCommit as remote origin
echo -e "${BLUE}🔗 Step 4: Configuring Git remote...${NC}"
if git remote get-url origin >/dev/null 2>&1; then
    echo "Updating existing origin remote..."
    git remote set-url origin "$CLONE_URL"
else
    echo "Adding new origin remote..."
    git remote add origin "$CLONE_URL"
fi
echo -e "${GREEN}✅ Git remote configured${NC}"

# Step 5: Push code to CodeCommit
echo -e "${BLUE}📤 Step 5: Pushing code to CodeCommit...${NC}"
echo "Pushing to main branch..."
git push -u origin main
echo -e "${GREEN}✅ Code pushed successfully${NC}"

# Step 6: Create Amplify App
echo -e "${BLUE}🏗️  Step 6: Creating Amplify application...${NC}"

# Check if app already exists
if aws amplify get-app --app-id $(aws amplify list-apps --query "apps[?name=='$APP_NAME'].appId" --output text) --region "$REGION" >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Amplify app '$APP_NAME' already exists${NC}"
    APP_ID=$(aws amplify list-apps --query "apps[?name=='$APP_NAME'].appId" --output text)
else
    echo "Creating new Amplify application..."
    APP_ID=$(aws amplify create-app \
        --name "$APP_NAME" \
        --description "In Scope Automation Tool - Modern React frontend for product compatibility analysis" \
        --repository "$CLONE_URL" \
        --platform "WEB" \
        --iam-service-role-arn "" \
        --enable-branch-auto-build \
        --query 'app.appId' \
        --output text \
        --region "$REGION")
    echo -e "${GREEN}✅ Amplify app created with ID: $APP_ID${NC}"
fi

# Step 7: Create main branch
echo -e "${BLUE}🌿 Step 7: Creating main branch in Amplify...${NC}"
if aws amplify get-branch --app-id "$APP_ID" --branch-name "main" --region "$REGION" >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Main branch already exists${NC}"
else
    aws amplify create-branch \
        --app-id "$APP_ID" \
        --branch-name "main" \
        --description "Production branch for In Scope Automation Tool" \
        --enable-auto-build \
        --enable-pull-request-preview \
        --stage "PRODUCTION" \
        --region "$REGION"
    echo -e "${GREEN}✅ Main branch created${NC}"
fi

# Step 8: Set environment variables
echo -e "${BLUE}⚙️  Step 8: Setting environment variables...${NC}"
aws amplify update-app \
    --app-id "$APP_ID" \
    --environment-variables "REACT_APP_API_URL=https://api.inscope.example.com/api,GENERATE_SOURCEMAP=false" \
    --region "$REGION"
echo -e "${GREEN}✅ Environment variables set${NC}"

# Step 9: Update build settings
echo -e "${BLUE}🔧 Step 9: Updating build settings...${NC}"
BUILD_SPEC='
{
  "version": 1,
  "applications": [
    {
      "frontend": {
        "phases": {
          "preBuild": {
            "commands": [
              "npm ci"
            ]
          },
          "build": {
            "commands": [
              "npm run build"
            ]
          }
        },
        "artifacts": {
          "baseDirectory": "build",
          "files": [
            "**/*"
          ]
        },
        "cache": {
          "paths": [
            "node_modules/**/*"
          ]
        }
      },
      "appRoot": "."
    }
  ]
}'

aws amplify update-app \
    --app-id "$APP_ID" \
    --build-spec "$BUILD_SPEC" \
    --region "$REGION"
echo -e "${GREEN}✅ Build settings updated${NC}"

# Step 10: Start first deployment
echo -e "${BLUE}🚀 Step 10: Starting deployment...${NC}"
JOB_ID=$(aws amplify start-job \
    --app-id "$APP_ID" \
    --branch-name "main" \
    --job-type "RELEASE" \
    --query 'jobSummary.jobId' \
    --output text \
    --region "$REGION")

echo -e "${GREEN}✅ Deployment started with Job ID: $JOB_ID${NC}"

# Get the app URL
APP_URL="https://main.$APP_ID.amplifyapp.com"

echo ""
echo "🎉 Deployment process completed!"
echo "================================================"
echo -e "${GREEN}📱 Application URL: $APP_URL${NC}"
echo -e "${BLUE}📊 Amplify Console: https://console.aws.amazon.com/amplify/home?region=$REGION#/$APP_ID${NC}"
echo -e "${YELLOW}📝 CodeCommit Repository: https://console.aws.amazon.com/codesuite/codecommit/repositories/$REPOSITORY_NAME/browse?region=$REGION${NC}"
echo ""
echo "🔄 Deployment Status:"
echo "You can monitor the deployment progress in the Amplify Console."
echo "The build typically takes 2-5 minutes to complete."
echo ""
echo "🔧 Next Steps:"
echo "1. Monitor the build progress in Amplify Console"
echo "2. Update REACT_APP_API_URL environment variable with your actual backend URL"
echo "3. Configure custom domain (optional)"
echo "4. Set up monitoring and alerts"
echo ""
echo "📚 Documentation:"
echo "- Frontend README: ./README.md"
echo "- Deployment Guide: ./DEPLOYMENT.md"
echo "- Design Updates: ./DESIGN_UPDATES.md"