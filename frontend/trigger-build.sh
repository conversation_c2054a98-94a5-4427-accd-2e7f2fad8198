#!/bin/bash

# Trigger new Amplify build after fixing configuration

echo "🔄 Triggering new Amplify build..."

# Get the app ID (you'll need to replace this with your actual app ID)
APP_ID=$(aws amplify list-apps --query "apps[?name=='in-scope-automation-tool'].appId" --output text)

if [ -z "$APP_ID" ]; then
    echo "❌ Could not find Amplify app. Please check the app name or set APP_ID manually."
    echo "💡 You can find your app ID in the Amplify console URL."
    exit 1
fi

echo "📱 Found Amplify app ID: $APP_ID"

# Start a new job
JOB_ID=$(aws amplify start-job \
    --app-id "$APP_ID" \
    --branch-name "main" \
    --job-type "RELEASE" \
    --query 'jobSummary.jobId' \
    --output text)

if [ $? -eq 0 ]; then
    echo "✅ Build triggered successfully!"
    echo "🔍 Job ID: $JOB_ID"
    echo "📊 Monitor progress at: https://console.aws.amazon.com/amplify/home?region=us-east-1#/$APP_ID"
    echo ""
    echo "🔄 Checking build status..."
    
    # Wait a moment then check status
    sleep 5
    aws amplify get-job --app-id "$APP_ID" --branch-name "main" --job-id "$JOB_ID" --query 'job.summary.status' --output text
else
    echo "❌ Failed to trigger build. Please check your AWS permissions."
fi