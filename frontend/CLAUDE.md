# Frontend CLAUDE.md

## Overview

The frontend is a React-based web application built with TypeScript that provides a user interface for the Auto In Scope Compatibility Analysis System. It allows users to submit analysis tasks and view their results through a modern, responsive interface.

## Key Architecture

### Technology Stack

- **React 19.1.0** with TypeScript for the main application framework
- **Ant Design 5.26.3** for UI components and design system
- **AWS Amplify 6.15.2** for AWS integration and hosting
- **React Router 7.6.3** for client-side routing
- **Axios 1.10.0** for API communication

### Core Components

- **App.tsx**: Main application component with routing setup and navigation
- **TaskSubmissionForm.tsx**: Form for submitting new compatibility analysis tasks
- **TaskViewPage.tsx**: Interface for viewing submitted tasks and their status
- **api.ts**: API service layer for backend communication

### Main Features

1. **Task Submission**
   - Product type pair selection from 26 predefined compatibility pairs
   - Dynamic prompt template loading based on selected pair
   - File upload (Excel, CSV, TSV) with direct S3 upload capability
   - Alternative S3 URI input for existing files
   - Real-time upload progress tracking

2. **Task Management**
   - View all submitted tasks with status tracking
   - Monitor task progress and completion
   - Download result files when tasks complete

3. **File Upload System**
   - Supports Excel (.xlsx, .xls), CSV, and TSV formats
   - 100MB file size limit
   - Presigned URL system for direct S3 uploads
   - Progress tracking during uploads

### Data Flow

1. User selects product type pair → API loads corresponding prompt template
2. User uploads data file → Frontend gets presigned URL → Direct S3 upload
3. User submits task → API creates task record in DynamoDB
4. Frontend polls task status → Displays progress and results

### API Integration

The frontend communicates with the FastAPI backend through the following endpoints:

- `GET /api/pt-pairs` - Get available product type pairs
- `POST /api/prompt` - Get prompt template for selected pair
- `POST /api/upload/presigned-url` - Get S3 presigned URL for file upload
- `POST /api/tasks` - Submit new analysis task
- `GET /api/tasks` - Get user's task list
- `GET /api/tasks/{id}` - Get specific task details

### Configuration

- **Environment Variables**
  - `REACT_APP_API_URL`: Backend API URL (defaults to localhost:8000)
  
- **Build Configuration**
  - Uses Create React App with TypeScript template
  - Configured for AWS Amplify deployment
  - Includes Jest testing framework setup

### Deployment

- **AWS Amplify**: Configured for continuous deployment
- **Build Scripts**: Standard React build process
- **Environment Setup**: Automated through `aws-env-setup.sh`

## Common Commands

```bash
# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Deploy to AWS Amplify
npm run deploy
```

## Development Notes

- Uses Ant Design's form validation and UI components
- Implements proper error handling and user feedback
- Responsive design supporting desktop and mobile
- TypeScript for type safety and better development experience
- Follows React best practices with hooks and functional components