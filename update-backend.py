#!/usr/bin/env python3
"""
Update backend with API changes
"""

import boto3
import time
import zipfile
import os
from botocore.exceptions import ClientError

def create_updated_package():
    """Create updated deployment package"""
    print("📦 Creating updated backend package...")
    
    backend_dir = "/home/<USER>/projects/auto_in_scope/backend"
    zip_path = "/tmp/auto-in-scope-backend-updated.zip"
    
    # Create zip file
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(backend_dir):
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if file.endswith(('.py', '.txt', '.yml', '.yaml', '.json', '.config', '.sh')) or file == 'Procfile':
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, backend_dir)
                    zipf.write(file_path, arc_name)
                    print(f"  Added: {arc_name}")
    
    print(f"✅ Updated package created: {zip_path}")
    return zip_path

def update_backend():
    """Update backend deployment"""
    eb_client = boto3.client('elasticbeanstalk')
    s3_client = boto3.client('s3')
    
    app_name = 'auto-in-scope-backend'
    env_name = 'auto-in-scope-backend-env'
    
    # Create package
    zip_path = create_updated_package()
    
    # Upload to S3
    bucket_name = 'auto-in-scope'
    key = f'eb-deployments/auto-in-scope-backend-updated-{int(time.time())}.zip'
    
    print(f"📤 Uploading to S3...")
    s3_client.upload_file(zip_path, bucket_name, key)
    
    # Create version
    version_label = f"v{int(time.time())}-updated"
    eb_client.create_application_version(
        ApplicationName=app_name,
        VersionLabel=version_label,
        Description='Auto In Scope Backend - Updated API with primary/accessory data',
        SourceBundle={'S3Bucket': bucket_name, 'S3Key': key},
        AutoCreateApplication=False,
        Process=True
    )
    
    print(f"✅ Created version: {version_label}")
    
    # Wait for processing
    print("⏳ Waiting for version processing...")
    for i in range(20):
        response = eb_client.describe_application_versions(
            ApplicationName=app_name,
            VersionLabels=[version_label]
        )
        if response['ApplicationVersions'] and response['ApplicationVersions'][0]['Status'] == 'PROCESSED':
            break
        time.sleep(10)
    
    # Update environment
    print(f"🚀 Updating environment...")
    eb_client.update_environment(
        ApplicationName=app_name,
        EnvironmentName=env_name,
        VersionLabel=version_label
    )
    
    # Wait for update
    print("⏳ Waiting for environment update...")
    for i in range(30):
        response = eb_client.describe_environments(
            ApplicationName=app_name,
            EnvironmentNames=[env_name]
        )
        
        if response['Environments']:
            env = response['Environments'][0]
            status = env['Status']
            health = env['Health']
            
            print(f"  Status: {status}, Health: {health}")
            
            if status == 'Ready':
                endpoint = env['EndpointURL']
                print(f"✅ Update complete: {endpoint}")
                
                # Cleanup
                os.remove(zip_path)
                return endpoint
        
        time.sleep(30)
    
    print("❌ Timeout waiting for update")
    return None

if __name__ == "__main__":
    endpoint = update_backend()
    if endpoint:
        print(f"\n🎉 Backend updated successfully!")
        print(f"Backend URL: http://{endpoint}")
        print(f"API Gateway: https://pbbo28cid2.execute-api.us-east-1.amazonaws.com/prod")
        print(f"\n🧪 Test the updated API:")
        print(f"curl https://pbbo28cid2.execute-api.us-east-1.amazonaws.com/prod/api/pt-pairs")
    else:
        print("\n❌ Update failed")