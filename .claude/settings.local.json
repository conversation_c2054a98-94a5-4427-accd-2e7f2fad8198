{"permissions": {"allow": ["Bash(npx create-react-app:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)", "Bash(grep:*)", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Ba<PERSON>(pip3:*)", "Bash(aws:*)", "Bash(conda activate:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(cat:*)", "Bash(find:*)", "Bash(ss:*)", "Bash(aws ec2 describe-security-groups:*)", "Bash(aws ec2 describe-instances:*)", "Bash(aws ec2 authorize-security-group-ingress:*)", "Bash(aws elbv2 describe-target-health:*)", "Bash(telnet:*)", "<PERSON><PERSON>(chmod:*)", "Bash(aws elasticbeanstalk describe-events:*)", "Bash(aws elasticbeanstalk describe-environments:*)", "Bash(git add:*)", "Bash(git commit:*)"], "deny": []}, "mcpServers": {"puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}}}