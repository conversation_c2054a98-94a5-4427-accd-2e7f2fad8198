# In Scope Automation Tool

A modern web application for analyzing product compatibility using advanced AI models. Built with React frontend and designed for AWS cloud deployment.

## 🚀 Features

- **Product Compatibility Analysis**: AI-powered analysis of product type pairs
- **Modern UI**: Glass-morphism design with responsive layout
- **File Upload**: Support for ZIP files and S3 URI inputs
- **Task Management**: Real-time task tracking and result downloads
- **AWS Integration**: Built for AWS Amplify deployment with DynamoDB backend

## 🏗 Architecture

```
├── frontend/                 # React TypeScript frontend
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── services/        # API services
│   │   ├── types/          # TypeScript definitions
│   │   └── ...
│   ├── amplify.yml         # AWS Amplify build config
│   └── package.json
├── src/                     # Python backend logic
│   ├── call_llm.py         # LLM integration
│   ├── build_prompt.py     # Prompt templates
│   └── ...
└── main.py                 # Main application entry
```

## 🛠 Technology Stack

### Frontend
- **React 19** with TypeScript
- **Ant Design** for UI components
- **React Router** for navigation
- **Axios** for API communication

### Backend (Legacy Python)
- **Python 3.x** with pandas, boto3
- **AWS Bedrock** for Claude LLM integration
- **S3** for file storage
- **DynamoDB** for task management

### Deployment
- **AWS Amplify** for frontend hosting
- **AWS API Gateway** for backend APIs
- **AWS Lambda** for serverless functions
- **AWS CodeCommit/GitHub** for CI/CD

## 🚀 Quick Start

### Local Development

1. **Frontend Setup**:
```bash
cd frontend
npm install
npm start
```

2. **Backend Setup**:
```bash
pip install -r requirements.txt
python main.py
```

### AWS Deployment

1. **Push to Repository**:
```bash
git add .
git commit -m "Initial deployment"
git push origin main
```

2. **Deploy via AWS Amplify**:
   - Connect your repository in Amplify Console
   - Configure build settings (use provided amplify.yml)
   - Set environment variables
   - Deploy!

## 📋 Product Type Pairs

The system supports 26 predefined product compatibility pairs:

- Air Conditioner ↔ HVAC Air Filter
- Digital Camera ↔ Flash Memory
- Cellular Phone ↔ Headphones
- Video Game Console ↔ Flash Memory
- Printer ↔ Inkjet/Laser Toner
- And 21 more combinations...

## 🔧 Configuration

### Environment Variables

```env
# Frontend (.env.production)
REACT_APP_API_URL=https://your-api-gateway-url/api

# Backend
AWS_REGION=us-east-1
S3_BUCKET=compatibility-data-gen
DYNAMODB_TABLE=claude_account
```

### AWS Services Required

- **Amplify**: Frontend hosting
- **S3**: File storage (compatibility-data-gen bucket)
- **DynamoDB**: Account and task management
- **Bedrock**: Claude LLM access
- **API Gateway**: REST API endpoints
- **Lambda**: Serverless functions

## 📖 API Documentation

### Endpoints

- `GET /api/pt-pairs` - Get available product type pairs
- `POST /api/prompt` - Get prompt template for PT pair
- `POST /api/tasks` - Submit analysis task
- `GET /api/tasks` - Get user's task list
- `GET /api/tasks/{id}` - Get task details

### Task Workflow

1. User selects product type pair
2. System loads appropriate prompt template
3. User uploads data file or provides S3 URI
4. Task is submitted and queued
5. AI analysis runs in background
6. Results are saved and made available for download

## 🔒 Security

- **HTTPS Only**: All communications encrypted
- **CORS**: Properly configured for frontend domain
- **IAM**: Least privilege access for AWS services
- **Input Validation**: File type and size restrictions
- **Error Handling**: Secure error messages

## 🎨 Design System

- **Color Palette**: Purple-blue gradient theme
- **Typography**: Modern font stack with proper hierarchy
- **Components**: Reusable design components
- **Responsive**: Mobile-first responsive design
- **Accessibility**: WCAG AA compliance

## 📈 Performance

- **Bundle Size**: Optimized build < 400KB gzipped
- **Loading**: Fast initial page load
- **Animations**: 60fps smooth animations
- **Caching**: Efficient resource caching

## 🧪 Testing

```bash
# Frontend tests
cd frontend
npm test

# Build verification
npm run build

# Linting
npm run lint
```

## 📚 Documentation

- [Frontend README](frontend/README.md)
- [Deployment Guide](frontend/DEPLOYMENT.md)
- [Design Updates](frontend/DESIGN_UPDATES.md)
- [UI Fixes](frontend/UI_FIXES.md)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is private and proprietary.

## 🆘 Support

For support and questions:
- Check the documentation files
- Review AWS Amplify logs
- Contact the development team

---

Built with ❤️ for efficient product compatibility analysis