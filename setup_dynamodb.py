#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create DynamoDB table for Auto In Scope project
"""
import boto3
import json
from botocore.exceptions import ClientError


def create_tasks_table():
    """Create the auto-in-scope-tasks DynamoDB table"""
    
    # Initialize DynamoDB client
    dynamodb = boto3.client('dynamodb', region_name='us-east-1')
    
    table_name = 'auto-in-scope-tasks'
    
    # Check if table already exists
    try:
        response = dynamodb.describe_table(TableName=table_name)
        print(f"Table {table_name} already exists!")
        return
    except ClientError as e:
        if e.response['Error']['Code'] != 'ResourceNotFoundException':
            print(f"Error checking table: {e}")
            return
    
    # Create table
    try:
        table = dynamodb.create_table(
            TableName=table_name,
            KeySchema=[
                {
                    'AttributeName': 'task_id',
                    'KeyType': 'HASH'  # Partition key
                }
            ],
            AttributeDefinitions=[
                {
                    'AttributeName': 'task_id',
                    'AttributeType': 'S'
                },
                {
                    'AttributeName': 'status',
                    'AttributeType': 'S'
                },
                {
                    'AttributeName': 'created_at',
                    'AttributeType': 'S'
                }
            ],
            GlobalSecondaryIndexes=[
                {
                    'IndexName': 'status-created_at-index',
                    'KeySchema': [
                        {
                            'AttributeName': 'status',
                            'KeyType': 'HASH'
                        },
                        {
                            'AttributeName': 'created_at',
                            'KeyType': 'RANGE'
                        }
                    ],
                    'Projection': {
                        'ProjectionType': 'ALL'
                    },
                    'ProvisionedThroughput': {
                        'ReadCapacityUnits': 5,
                        'WriteCapacityUnits': 5
                    }
                }
            ],
            ProvisionedThroughput={
                'ReadCapacityUnits': 5,
                'WriteCapacityUnits': 5
            }
        )
        
        print(f"Creating table {table_name}...")
        
        # Wait for table to be created
        waiter = dynamodb.get_waiter('table_exists')
        waiter.wait(TableName=table_name)
        
        print(f"✅ Table {table_name} created successfully!")
        
        # Print table details
        response = dynamodb.describe_table(TableName=table_name)
        print("\nTable Details:")
        print(f"Table Name: {response['Table']['TableName']}")
        print(f"Table Status: {response['Table']['TableStatus']}")
        print(f"Table ARN: {response['Table']['TableArn']}")
        
    except ClientError as e:
        print(f"❌ Error creating table: {e}")


def print_table_schema():
    """Print the expected table schema for reference"""
    
    schema = {
        "TableName": "auto-in-scope-tasks",
        "KeySchema": {
            "PartitionKey": "task_id (String)"
        },
        "Attributes": {
            "task_id": "String (Primary Key)",
            "pt_pair": "String",
            "prompt_text": "String", 
            "s3_uri": "String",
            "status": "String (waiting|processing|completed|failed)",
            "created_at": "String (ISO datetime)",
            "updated_at": "String (ISO datetime, optional)",
            "completed_at": "String (ISO datetime, optional)",
            "result_file_url": "String (optional)",
            "error_message": "String (optional)",
            "progress": "Number (0-100)",
            "total_items": "Number",
            "processed_items": "Number"
        },
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "status-created_at-index",
                "PartitionKey": "status",
                "SortKey": "created_at",
                "Purpose": "Efficient querying by status and creation time"
            }
        ]
    }
    
    print("\n" + "="*60)
    print("EXPECTED TABLE SCHEMA")
    print("="*60)
    print(json.dumps(schema, indent=2))
    print("="*60)


def main():
    """Main function"""
    print("Auto In Scope - DynamoDB Setup")
    print("="*40)
    
    # Print schema first
    print_table_schema()
    
    # Create table
    create_tasks_table()
    
    print("\n✅ DynamoDB setup completed!")
    print("\nNext steps:")
    print("1. Ensure your AWS credentials have DynamoDB permissions")
    print("2. Start the backend API: cd backend && python run_api.py")
    print("3. Start the worker service: cd worker && python start_worker.py")
    print("4. Start the frontend: cd frontend && npm start")


if __name__ == "__main__":
    main()