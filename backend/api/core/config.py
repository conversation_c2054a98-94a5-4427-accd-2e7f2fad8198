from pydantic_settings import BaseSettings
import os


class Settings(BaseSettings):
    # API configuration
    API_V1_PREFIX: str = "/api"
    PROJECT_NAME: str = "Auto In Scope API"
    VERSION: str = "1.0.0"
    
    # AWS configuration
    AWS_REGION: str = "us-east-1"
    AWS_S3_BUCKET: str = "auto-in-scope"
    AWS_TASK_TABLE: str = "auto-in-scope-tasks"
    
    # CORS configuration
    CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:3001",
        "https://main.d1zh6k9f6fikvg.amplifyapp.com",  # Amplify URL
        "https://main.d1zh6k9f6fikvg.amplifyapp.com/",  # Amplify URL with trailing slash
        "*",  # Allow all origins for now (remove in production)
    ]
    
    # File upload configuration
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()