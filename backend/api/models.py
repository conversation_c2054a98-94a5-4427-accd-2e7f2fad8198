from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Any, Dict
from enum import Enum
from datetime import datetime
import uuid


class TaskStatus(str, Enum):
    WAITING = "waiting"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class PTairModel(BaseModel):
    value: str
    label: str
    primary_pt: str
    accessory_pt: str


class PromptRequestModel(BaseModel):
    pt_pair: str


class PromptResponseModel(BaseModel):
    prompt_template: str
    compatibility_criterion: Dict[str, Any]


class TaskSubmissionModel(BaseModel):
    user_id: str
    pt_pair: str
    prompt_text: str
    model: str
    file_path: Optional[str] = None
    s3_uri: Optional[str] = None


class TaskModel(BaseModel):
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str
    pt_pair: str
    prompt_text: str
    model: str
    file_path: Optional[str] = None
    s3_uri: Optional[str] = None
    status: TaskStatus = TaskStatus.WAITING
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result_file_url: Optional[str] = None
    error_message: Optional[str] = None
    progress: Optional[int] = 0
    total_items: Optional[int] = 0
    processed_items: Optional[int] = 0


class TaskCreateResponse(BaseModel):
    task_id: str
    message: str = "Task submitted successfully"


class FileUploadResponse(BaseModel):
    s3_uri: str
    filename: str
    size: int


class ErrorResponse(BaseModel):
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None


class PresignedUrlRequest(BaseModel):
    filename: str
    content_type: str
    file_size: int


class PresignedUrlResponse(BaseModel):
    presigned_url: str
    s3_uri: str
    expires_in: int


class HealthCheckResponse(BaseModel):
    status: str
    timestamp: datetime
    version: str