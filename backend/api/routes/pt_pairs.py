from fastapi import APIRouter, HTTPException
from typing import List
import boto3
from botocore.exceptions import ClientError
import logging

from ..models import PTairModel
from ..core.config import settings

router = APIRouter()


# Configure logging
logger = logging.getLogger(__name__)


def get_pt_pairs_from_dynamodb() -> List[PTairModel]:
    """Get PT pairs data from DynamoDB"""
    try:
        # Initialize DynamoDB client
        dynamodb = boto3.resource('dynamodb', region_name=settings.AWS_REGION)
        table = dynamodb.Table('auto-in-scope-pt-pairs')
        
        # Scan all PT pairs from the table
        response = table.scan()
        items = response['Items']
        
        # Handle pagination if needed
        while 'LastEvaluatedKey' in response:
            response = table.scan(ExclusiveStartKey=response['LastEvaluatedKey'])
            items.extend(response['Items'])
        
        if not items:
            raise Exception("No PT pairs found in DynamoDB. Please ensure the sync service is running.")
        
        # Convert DynamoDB items to PTairModel objects
        pt_pairs = []
        for item in items:
            try:
                pt_pair_model = PTairModel(
                    value=item['pt_pair'].lower(),
                    label=item.get('label', item['pt_pair']),
                    primary_pt=item.get('primary_pt', ''),
                    accessory_pt=item.get('accessory_pt', '')
                )
                pt_pairs.append(pt_pair_model)
                
            except Exception as e:
                logger.warning(f"Error processing PT pair item {item.get('pt_pair', 'unknown')}: {e}")
                continue
        
        if not pt_pairs:
            raise Exception("No valid PT pairs could be processed from DynamoDB")
        
        # Sort PT pairs by label for better user experience
        pt_pairs.sort(key=lambda x: x.label)
        
        logger.info(f"Successfully loaded {len(pt_pairs)} PT pairs from DynamoDB")
        return pt_pairs
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'ResourceNotFoundException':
            raise HTTPException(
                status_code=503,
                detail="PT pairs table not found. Please contact administrator to set up the sync service."
            )
        else:
            logger.error(f"DynamoDB error: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Database error while loading PT pairs: {str(e)}"
            )
    except Exception as e:
        logger.error(f"Error loading PT pairs from DynamoDB: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error loading PT pairs: {str(e)}"
        )


@router.get("/pt-pairs", response_model=List[PTairModel])
async def get_pt_pairs_endpoint():
    """
    Get list of available product type pairs for compatibility analysis.
    This reads from DynamoDB which is kept in sync with build_prompt.py by the worker service.
    """
    try:
        return get_pt_pairs_from_dynamodb()
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_pt_pairs_endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to load PT pairs: {str(e)}"
        )