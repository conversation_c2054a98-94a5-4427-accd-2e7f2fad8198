from fastapi import APIRouter, HTTPException
import boto3
from botocore.exceptions import ClientError
import logging

from ..models import PromptRequestModel, PromptResponseModel
from ..core.config import settings

router = APIRouter()

# Configure logging
logger = logging.getLogger(__name__)


@router.post("/prompt", response_model=PromptResponseModel)
async def get_prompt_template(request: PromptRequestModel):
    """
    Get prompt template and compatibility criteria for a specific PT pair.
    This reads from DynamoDB which is kept in sync with build_prompt.py by the worker service.
    """
    try:
        # Convert to uppercase for DynamoDB lookup
        pt_pair_upper = request.pt_pair.upper()
        
        # Initialize DynamoDB client
        dynamodb = boto3.resource('dynamodb', region_name=settings.AWS_REGION)
        table = dynamodb.Table('auto-in-scope-pt-pairs')
        
        # Get PT pair data from DynamoDB
        response = table.get_item(Key={'pt_pair': pt_pair_upper})
        
        if 'Item' not in response:
            raise HTTPException(
                status_code=404,
                detail=f"PT pair '{request.pt_pair}' not found in database. Please ensure the sync service is running."
            )
        
        item = response['Item']
        
        # Extract data from DynamoDB item
        compatibility_criterion = item.get('compatibility_criterion', {})
        primary_template = item.get('primary_template', '')
        accessory_template = item.get('accessory_template', '')
        
        # Validate that we have the required data
        if not compatibility_criterion or not primary_template or not accessory_template:
            raise HTTPException(
                status_code=500,
                detail=f"Incomplete data for PT pair '{request.pt_pair}'. Please contact administrator."
            )
        
        # Use primary template as the main template for backward compatibility
        prompt_template = primary_template
        
        # Add structured data for frontend parsing
        enhanced_criterion = {
            **compatibility_criterion,
            "primary_template": primary_template,
            "accessory_template": accessory_template
        }
        
        logger.info(f"Successfully loaded prompt template for PT pair: {request.pt_pair}")
        
        return PromptResponseModel(
            prompt_template=prompt_template,
            compatibility_criterion=enhanced_criterion
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'ResourceNotFoundException':
            raise HTTPException(
                status_code=503,
                detail="PT pairs table not found. Please contact administrator to set up the sync service."
            )
        else:
            logger.error(f"DynamoDB error: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Database error while loading prompt template: {str(e)}"
            )
    except Exception as e:
        logger.error(f"Error loading prompt template for PT pair {request.pt_pair}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating prompt template: {str(e)}"
        )