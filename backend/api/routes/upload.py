from fastapi import APIRouter, HTTPException
import boto3
import uuid
from datetime import datetime
import logging
import os

from ..models import PresignedUrlRequest, PresignedUrlResponse
from ..core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

# Initialize S3 client
s3_client = boto3.client('s3', region_name=settings.AWS_REGION)


@router.post("/upload/presigned-url", response_model=PresignedUrlResponse)
async def get_presigned_url(request: PresignedUrlRequest):
    """
    Generate S3 presigned URL for direct file upload
    """
    try:
        # Validate file size
        if request.file_size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
            )
        
        # Validate file type
        allowed_extensions = ['.xlsx', '.xls', '.csv', '.tsv', 'zip']
        file_extension = os.path.splitext(request.filename)[1].lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File type '{file_extension}' not supported. Allowed types: {allowed_extensions}"
            )
        
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        safe_filename = f"uploads/{timestamp}_{unique_id}_{request.filename}"
        
        # Generate presigned URL for PUT operation
        presigned_url = s3_client.generate_presigned_url(
            'put_object',
            Params={
                'Bucket': settings.AWS_S3_BUCKET,
                'Key': safe_filename,
                'ContentType': request.content_type,
                'ContentLength': request.file_size
            },
            ExpiresIn=3600  # 1 hour
        )
        
        # Generate S3 URI
        s3_uri = f"s3://{settings.AWS_S3_BUCKET}/{safe_filename}"
        
        logger.info(f"Generated presigned URL for: {safe_filename}")
        
        return PresignedUrlResponse(
            presigned_url=presigned_url,
            s3_uri=s3_uri,
            expires_in=3600
        )
        
    except Exception as e:
        logger.error(f"Error generating presigned URL: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating presigned URL: {str(e)}"
        )