from fastapi import APIRouter, HTTPException
from typing import List, Optional
import boto3
import uuid
from datetime import datetime
import logging

from ..models import (
    TaskModel, TaskCreateResponse, TaskStatus, TaskSubmissionModel
)
from ..core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

# Initialize AWS services
dynamodb = boto3.resource('dynamodb', region_name=settings.AWS_REGION)
task_table = dynamodb.Table(settings.AWS_TASK_TABLE)


@router.post("/tasks", response_model=TaskCreateResponse)
async def submit_task(task_data: TaskSubmissionModel):
    """
    Submit a new analysis task - saves to DynamoDB for worker processing
    """
    try:
        # Generate custom task ID based on user_id and current timestamp
        from datetime import datetime
        now = datetime.now()
        task_id = f"{task_data.user_id}_{now.strftime('%Y%m%d_%H%M%S')}"
        
        # Validate S3 URI
        if not task_data.s3_uri or not task_data.s3_uri.startswith('s3://'):
            raise HTTPException(
                status_code=400,
                detail="Valid S3 URI is required"
            )
        
        # Create task model
        task = TaskModel(
            task_id=task_id,
            user_id=task_data.user_id,
            pt_pair=task_data.pt_pair,
            prompt_text=task_data.prompt_text,
            model=task_data.model,
            s3_uri=task_data.s3_uri,
            status=TaskStatus.WAITING
        )
        
        # Save task to DynamoDB
        item = {
            'task_id': task.task_id,
            'user_id': task.user_id,
            'pt_pair': task.pt_pair,
            'prompt_text': task.prompt_text,
            'model': task.model,
            's3_uri': task.s3_uri,
            'status': task.status.value,
            'created_at': task.created_at.isoformat(),
            'progress': task.progress or 0,
            'total_items': task.total_items or 0,
            'processed_items': task.processed_items or 0
        }
        
        # Remove None values
        item = {k: v for k, v in item.items() if v is not None}
        
        task_table.put_item(Item=item)
        
        logger.info(f"Task {task_id} submitted successfully to DynamoDB")
        
        return TaskCreateResponse(
            task_id=task_id,
            message="Task submitted successfully and queued for processing"
        )
        
    except Exception as e:
        logger.error(f"Error submitting task: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error submitting task: {str(e)}"
        )


@router.get("/tasks", response_model=List[TaskModel])
async def get_tasks(user_id: Optional[str] = None):
    """
    Get list of tasks from DynamoDB
    """
    try:
        if user_id:
            response = task_table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('user_id').eq(user_id)
            )
        else:
            response = task_table.scan()
        
        tasks = []
        for item in response['Items']:
            task = TaskModel(
                task_id=item['task_id'],
                user_id=item.get('user_id'),
                pt_pair=item['pt_pair'],
                prompt_text=item['prompt_text'],
                model=item.get('model', 'Claude 3.5 Sonnet V2'),  # Default for existing tasks
                file_path=item.get('file_path'),
                s3_uri=item.get('s3_uri'),
                status=TaskStatus(item['status']),
                created_at=datetime.fromisoformat(item['created_at']),
                updated_at=datetime.fromisoformat(item['updated_at']) if item.get('updated_at') else None,
                completed_at=datetime.fromisoformat(item['completed_at']) if item.get('completed_at') else None,
                result_file_url=item.get('result_file_url'),
                error_message=item.get('error_message'),
                progress=item.get('progress', 0),
                total_items=item.get('total_items', 0),
                processed_items=item.get('processed_items', 0)
            )
            tasks.append(task)
        
        # Sort by creation time (newest first)
        tasks.sort(key=lambda x: x.created_at, reverse=True)
        
        return tasks
        
    except Exception as e:
        logger.error(f"Error retrieving tasks: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving tasks: {str(e)}"
        )


@router.get("/tasks/{task_id}", response_model=TaskModel)
async def get_task(task_id: str):
    """
    Get specific task details from DynamoDB
    """
    try:
        response = task_table.get_item(Key={'task_id': task_id})
        
        if 'Item' not in response:
            raise HTTPException(
                status_code=404,
                detail=f"Task {task_id} not found"
            )
        
        item = response['Item']
        
        task = TaskModel(
            task_id=item['task_id'],
            user_id=item.get('user_id'),
            pt_pair=item['pt_pair'],
            prompt_text=item['prompt_text'],
            model=item.get('model', 'Claude 3.5 Sonnet V2'),  # Default for existing tasks
            file_path=item.get('file_path'),
            s3_uri=item.get('s3_uri'),
            status=TaskStatus(item['status']),
            created_at=datetime.fromisoformat(item['created_at']),
            updated_at=datetime.fromisoformat(item['updated_at']) if item.get('updated_at') else None,
            completed_at=datetime.fromisoformat(item['completed_at']) if item.get('completed_at') else None,
            result_file_url=item.get('result_file_url'),
            error_message=item.get('error_message'),
            progress=item.get('progress', 0),
            total_items=item.get('total_items', 0),
            processed_items=item.get('processed_items', 0)
        )
        
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving task {task_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving task: {str(e)}"
        )