# Backend CLAUDE.md

## Overview

The backend is a FastAPI-based REST API that serves as the web interface for the Auto In Scope Compatibility Analysis System. It provides endpoints for task submission, file uploads, and task management, acting as a bridge between the frontend and the worker processes.

## Key Architecture

### Technology Stack

- **FastAPI** - Modern, fast web framework for Python APIs
- **AWS SDK (Boto3)** - AWS service integration
- **DynamoDB** - Task storage and management
- **S3** - File storage for uploads and results
- **Uvicorn** - ASGI server for running the FastAPI application

### Core Components

- **app.py**: Main FastAPI application with middleware and exception handling
- **models.py**: Pydantic models for request/response validation
- **core/config.py**: Configuration management and settings
- **routes/**: API endpoint handlers organized by functionality

### API Endpoints

#### Task Management (`/api/tasks`)
- `POST /api/tasks` - Submit new compatibility analysis task
- `GET /api/tasks` - Get list of user's tasks
- `GET /api/tasks/{task_id}` - Get specific task details

#### File Upload (`/api/upload`)
- `POST /api/upload/presigned-url` - Get S3 presigned URL for direct uploads

#### Product Type Pairs (`/api/pt-pairs`)
- `GET /api/pt-pairs` - Get available product type pairs

#### Prompts (`/api/prompt`)
- `POST /api/prompt` - Get prompt template for selected PT pair

### Data Models

#### Task Model
- `task_id`: Unique identifier
- `pt_pair`: Product type pair (e.g., "PRINTER-LASER_PRINTER_TONER")
- `prompt_text`: Customized prompt template
- `s3_uri`: Location of input data file
- `status`: Task status (waiting, processing, completed, failed)
- `progress`: Processing progress percentage
- `result_file_url`: S3 URL of results file

#### Task Status Flow
1. **waiting** - Task submitted, waiting for worker pickup
2. **processing** - Worker is processing the task
3. **completed** - Task finished successfully
4. **failed** - Task encountered an error

### AWS Integration

#### DynamoDB
- **Task Table**: Stores task metadata and status
- **Partition Key**: `task_id`
- **Attributes**: All task fields including timestamps and progress

#### S3 Storage
- **Input Files**: User uploads stored in `compatibility-data-gen` bucket
- **Result Files**: Analysis results stored in `results/` prefix
- **Presigned URLs**: Secure direct upload capability

### Configuration

Environment variables and settings managed through `core/config.py`:

- `PROJECT_NAME`: Application name
- `VERSION`: API version
- `CORS_ORIGINS`: Allowed frontend origins
- `AWS_REGION`: AWS region for services
- `AWS_TASK_TABLE`: DynamoDB table name
- `AWS_S3_BUCKET`: S3 bucket for file storage

### Error Handling

Comprehensive error handling with structured responses:

- **HTTP Exception Handler**: Standardized HTTP error responses
- **Validation Exception Handler**: Request validation error formatting
- **General Exception Handler**: Catches unexpected errors with logging

### Request/Response Flow

1. **Task Submission**:
   - Frontend sends PT pair + prompt + S3 URI
   - Backend validates request
   - Creates task record in DynamoDB with "waiting" status
   - Returns task ID to frontend

2. **File Upload**:
   - Frontend requests presigned URL
   - Backend generates S3 presigned URL
   - Frontend uploads directly to S3
   - S3 URI used in task submission

3. **Task Monitoring**:
   - Frontend polls task status
   - Backend queries DynamoDB for current status
   - Returns progress and completion information

### Security Features

- **CORS Configuration**: Restricts cross-origin requests
- **Request Validation**: Pydantic models validate all inputs
- **Presigned URLs**: Secure, time-limited S3 access
- **Error Sanitization**: Prevents sensitive information leakage

## Common Commands

```bash
# Start development server
python run_api.py

# Start with uvicorn directly
uvicorn api.app:app --host 0.0.0.0 --port 8000 --reload

# Install dependencies
pip install -r requirements.txt
```

## Development Notes

- FastAPI provides automatic API documentation at `/docs`
- Async/await patterns for better performance
- Structured logging for debugging and monitoring
- Modular design with separate route handlers
- Environment-based configuration for different deployment stages
- Integration with worker processes through DynamoDB task queue