# Auto In Scope - Deployment Guide

## Architecture Overview

The system consists of 3 main components:

1. **Frontend** (React) - User interface for task submission and monitoring
2. **Backend API** (FastAPI) - Handles requests and stores tasks in DynamoDB  
3. **Worker Service** - Monitors DynamoDB and processes analysis tasks

## New File Upload Flow

The system now uses S3 presigned URLs for direct file uploads:

1. User selects a file in the frontend
2. Frontend requests a presigned URL from backend API
3. Frontend uploads file directly to S3 using presigned URL
4. Frontend submits task with S3 URI to backend API
5. Backend stores task in DynamoDB with S3 URI
6. Worker processes task by reading file from S3 URI

## Prerequisites

### AWS Services Required
- **DynamoDB**: Table `auto-in-scope-tasks` with primary key `task_id`
- **DynamoDB**: Table `claude_account` (existing)
- **S3**: Bucket `auto-in-scope` 
- **AWS Credentials**: Configured with appropriate permissions

### Required AWS Permissions
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "dynamodb:PutItem",
                "dynamodb:GetItem",
                "dynamodb:UpdateItem",
                "dynamodb:Scan",
                "dynamodb:Query"
            ],
            "Resource": [
                "arn:aws:dynamodb:*:*:table/auto-in-scope-tasks",
                "arn:aws:dynamodb:*:*:table/auto-in-scope-tasks/index/*",
                "arn:aws:dynamodb:*:*:table/claude_account"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject"
            ],
            "Resource": "arn:aws:s3:::auto-in-scope/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel"
            ],
            "Resource": "*"
        }
    ]
}
```

## Setup Instructions

### 1. Create DynamoDB Table

```bash
python setup_dynamodb.py
```

This creates the `auto-in-scope-tasks` table with the following schema:
- **Primary Key**: `task_id` (String)
- **Global Secondary Index**: `status-created_at-index` for efficient querying
- **Attributes**: All task-related fields including status, timestamps, etc.

### 2. Backend API Setup

```bash
cd backend
pip install -r requirements.txt
python run_api.py
```

The API will be available at http://localhost:8000
- Swagger docs: http://localhost:8000/docs
- Health check: http://localhost:8000/health

### 3. Worker Service Setup

```bash
cd worker
pip install -r requirements.txt
python start_worker.py
```

### 4. Frontend Setup

```bash
cd frontend
npm install
npm start
```

## API Endpoints

### GET /api/pt-pairs
Returns list of available product type pairs (dynamically loaded from build_prompt.py)

### POST /api/prompt  
Get prompt template for specific PT pair

### POST /api/upload/presigned-url
Generate S3 presigned URL for file upload
```json
{
  "filename": "data.xlsx",
  "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "file_size": 1024000
}
```

Response:
```json
{
  "presigned_url": "https://s3.amazonaws.com/...",
  "s3_uri": "s3://bucket/path/file.xlsx",
  "expires_in": 3600
}
```

### POST /api/tasks
Submit new analysis task with S3 URI
```json
{
  "pt_pair": "camera_digital-flash_memory",
  "prompt_text": "Analyze compatibility...",
  "s3_uri": "s3://bucket/path/file.xlsx"
}
```

### GET /api/tasks
Get list of all tasks

### GET /api/tasks/{task_id}
Get specific task details

## File Upload Process

### Frontend Flow
1. User selects file (Excel, CSV, TSV - max 100MB)
2. Frontend validates file type and size
3. Frontend calls `/api/upload/presigned-url` to get upload URL
4. Frontend uploads file directly to S3 using presigned URL
5. Frontend shows upload progress and success confirmation
6. Frontend stores S3 URI for task submission

### Supported File Types
- Excel: `.xlsx`, `.xls`
- CSV: `.csv`
- TSV: `.tsv`
- Maximum size: 100MB

## Configuration

### Backend (.env)
```
AWS_REGION=us-east-1
AWS_S3_BUCKET=auto-in-scope
AWS_TASK_TABLE=auto-in-scope-tasks
CORS_ORIGINS=["http://localhost:3000","https://your-frontend-domain.com"]
MAX_FILE_SIZE=104857600
```

### Worker (config.py)
```python
AWS_REGION = "us-east-1"
AWS_S3_BUCKET = "auto-in-scope"
AWS_TASK_TABLE = "auto-in-scope-tasks"
MAX_CONCURRENT_TASKS = 3
POLL_INTERVAL = 10
DEFAULT_MODEL = "Claude 3.5 Sonnet V2"
```

### Frontend (.env)
```
REACT_APP_API_URL=http://localhost:8000/api
```

## Task Status Flow

1. **waiting** - Task submitted, waiting for worker
2. **processing** - Worker is processing the task
3. **completed** - Task finished successfully with results
4. **failed** - Task failed with error message

## Monitoring

### Worker Logs
```bash
tail -f worker/worker_YYYYMMDD.log
```

### API Logs
Check console output for API requests and errors

### Task Status Monitoring
```bash
# Check waiting tasks
aws dynamodb scan --table-name auto-in-scope-tasks --filter-expression "#status = :status" --expression-attribute-names '{"#status": "status"}' --expression-attribute-values '{":status": {"S": "waiting"}}'

# Check processing tasks  
aws dynamodb scan --table-name auto-in-scope-tasks --filter-expression "#status = :status" --expression-attribute-names '{"#status": "status"}' --expression-attribute-values '{":status": {"S": "processing"}}'
```

## Troubleshooting

### Common Issues

1. **File upload fails**
   - Check S3 bucket permissions
   - Verify file size under 100MB
   - Ensure supported file format
   - Check presigned URL expiration (1 hour)

2. **Tasks stuck in waiting status**
   - Verify worker service is running
   - Check DynamoDB table permissions
   - Review worker logs for errors

3. **Presigned URL generation fails**
   - Verify AWS credentials have S3 permissions
   - Check S3 bucket exists and is accessible
   - Ensure correct region configuration

4. **LLM processing errors**
   - Check AWS Bedrock permissions
   - Verify claude_account table has valid credentials
   - Confirm selected model availability in region

### Service Restart Commands

```bash
# Restart API
pkill -f "run_api.py"
cd backend && python run_api.py

# Restart Worker  
pkill -f "start_worker.py"
cd worker && python start_worker.py

# Restart Frontend
cd frontend && npm start
```

## Production Deployment

### Using Process Manager (PM2)
```bash
# Install PM2
npm install -g pm2

# Start services
pm2 start backend/run_api.py --name "auto-in-scope-api" --interpreter python3
pm2 start worker/start_worker.py --name "auto-in-scope-worker" --interpreter python3

# Monitor
pm2 status
pm2 logs
pm2 restart all
```

### Environment Variables for Production
- Set appropriate CORS origins for production frontend domain
- Configure proper AWS credentials (IAM roles recommended)
- Set log levels appropriately
- Configure resource limits for concurrent processing

### Security Considerations
- Use IAM roles instead of access keys when possible
- Implement proper CORS configuration
- Set up CloudTrail for API monitoring
- Configure S3 bucket policies for restricted access
- Use VPC endpoints for DynamoDB/S3 access in private networks