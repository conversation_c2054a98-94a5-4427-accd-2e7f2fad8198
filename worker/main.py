import re
import os
import sys
import boto3
import pandas as pd
from tqdm import tqdm
import json
import concurrent.futures
BASE_DIR = os.path.dirname(os.path.abspath("{}/".format(__file__)))
sys.path.append(BASE_DIR)

from src.call_llm import LLM
from src.build_prompt import main_prompt, get_comp_criterion
from src.utils import read_input_data, layer_sample, calculate_gv_ceiling
from src.crawl_images import upload_file


def in_scope_extract(claude_output):
    try:
        if claude_output == "no output":
            return "no output"
        claude_output = re.search(r"\{.*?\}", claude_output).group(0)
        in_scope_res = json.loads(claude_output)["in_scope"]
        # reason_res = json.loads(claude_output)["reason"]
    except Exception as ex:
        in_scope_res = "unknown"
        # reason_res = "unknown"

    return in_scope_res


def main(config):
    mp_mapping = {
        "us": "1",
        "uk": "3",
        "de": "4",
        "fr": "5",
        "ca": "7",
        "it": "35691",
        "es": "44551",
    }
    # data_df = read_input_data(f"{BASE_DIR}/data/{config['pt_pair']}/{config['current_pt']}_{config['acc_pri']}_pt_data_{mp_mapping[config['mp']]}.tsv000")
    data_df = pd.read_excel(f"{BASE_DIR}/data/raw_data/{datetime}/{config['current_pt']}_accessory_pt_data_{mp_mapping[config['mp']]}_{datetime}.xlsx")
    print(f"数据集大小: {len(data_df)}")
    # if len(data_df) > 8000:
    #     data_df = layer_sample(data_df)

    print(f"采样后数据集大小: {len(data_df)}")
    if len(data_df) == 0:
        print(f"数据集为空，跳过")
        return

    # collect the images and upload them to s3
    print("Start to collect images and upload them to s3")
    asins = data_df['asin'].tolist()
    asins = [(asin, mp) for asin in asins]

    with concurrent.futures.ProcessPoolExecutor(max_workers=20) as executor, tqdm(total=len(asins), desc='Getting Image') as bar:
        future_to_complete = {executor.submit(upload_file, asin_info[0], asin_info[1], config["current_pt"], config["acc_pri"]): asin_info for asin_info in asins}
        for future in concurrent.futures.as_completed(future_to_complete):
            result = future.result()
            bar.update(1)

    print("Image collection and upload completed")
    

    llm.multi_thrad_run( data_df, f"{BASE_DIR}/data/{datetime}/{current_pt}_{mp}_{datetime}_log.json")

    # find the asins without resutls and re-run the script
    with open(f"{BASE_DIR}/data/{datetime}/{current_pt}_{mp}_{datetime}_log.json", "r") as f:
        claude_response = f.readlines()
        claude_response = [json.loads(line) for line in claude_response]

    claude_response_df = pd.DataFrame(claude_response)
    # claude_response_df = claude_response_df[claude_response_df["claude_response"] != "no output"]

    claude_response_df["in_scope"] = claude_response_df["claude_response"].apply(in_scope_extract)

    gv_ceiling = calculate_gv_ceiling(data_df, claude_response_df)
    print(f"{config['pt_pair']} Total GV Ceiling: {gv_ceiling}")
    
    with open(f"{BASE_DIR}/data/gv_ceiling_output.txt", "a+") as f:
        f.write(f"{config['pt_pair']} - {config['acc_pri']} - {config['mp']} - Total GV Ceiling: {gv_ceiling}\n")

    claude_response_df.to_excel(f"{BASE_DIR}/data/{datetime}/{config['current_pt']}_{config['mp']}_{datetime}_in_scope_result.xlsx", index=False)


if __name__ == "__main__":
    pt_pairs = ["AIR_CONDITIONER-HVAC_AIR_FILTER", "CAMCORDER-CAMERA_ENCLOSURE", "CAMCORDER-CAMERA_LENS_FILTERS", "CAMCORDER-CAMERA_SUPPORT", "CAMCORDER-FLASH_MEMORY", "CAMERA_DIGITAL-CAMERA_ENCLOSURE", "CAMERA_DIGITAL-CAMERA_FLASH", "CAMERA_DIGITAL-CAMERA_LENS_FILTERS", "CAMERA_DIGITAL-FLASH_MEMORY", "CAMERA_FILM-PHOTOGRAPHIC_FILM", "CELLULAR_PHONE-FLASH_MEMORY", "CELLULAR_PHONE-HEADPHONES", "DIGITAL_VIDEO_RECORDER-COMPUTER_DRIVE_OR_STORAGE", "GPS_OR_NAVIGATION_SYSTEM-FLASH_MEMORY", "NOTEBOOK_COMPUTER-COMPUTER_DRIVE_OR_STORAGE", "NOTEBOOK_COMPUTER-FLASH_MEMORY", "PERSONAL_COMPUTER-COMPUTER_DRIVE_OR_STORAGE", "PERSONAL_COMPUTER-FLASH_MEMORY", "PERSONAL_COMPUTER-INPUT_MOUSE", "PRINTER-INKJET_PRINTER_INK", "PRINTER-LASER_PRINTER_TONER", "SECURITY_CAMERA-FLASH_MEMORY", "TELEVISION-HEADPHONES", "VENT_HOOD-HVAC_AIR_FILTER", "VIDEO_GAME_CONSOLE-FLASH_MEMORY", "VIDEO_GAME_CONSOLE-HEADPHONES"]
    llm = LLM(model_name="Claude 3.5 Sonnet V2")
    datetime = "20250531"

    for pt_pair in tqdm(pt_pairs):
        for mp in ["us", "uk", "de", "fr", "ca", "it", "es"]:
            print(f"now the pt_pair {pt_pair} - {mp} is being processed")
            primary_pt = pt_pair.split("-")[0].lower()
            accessory_pt = pt_pair.split("-")[1].lower()
            for index, current_pt in enumerate([accessory_pt]):
                upper_pt_pair = pt_pair.upper()
                compatibility_criterion = get_comp_criterion(upper_pt_pair)

                # if index == 0:
                #     acc_pri = "accessory"
                # else:
                acc_pri = "accessory"

                # if os.path.exists(f"{BASE_DIR}/data/{pt_pair}/{pt_pair}_{acc_pri}_in_scope_total_{mp}.xlsx"):
                #     continue

                config = {
                    "pt_pair": pt_pair.lower(),
                    "primary_pt": primary_pt,
                    "accessory_pt": accessory_pt,
                    "current_pt": current_pt,
                    "acc_pri": acc_pri,
                    "compatibility_criterion": compatibility_criterion,
                    "mp": mp
                }

                # read existing claude response
                if os.path.exists(f"{BASE_DIR}/data/{datetime}/{current_pt}_{mp}_{datetime}_log.json"):
                    with open(f"{BASE_DIR}/data/{datetime}/{current_pt}_{mp}_{datetime}_log.json", "r") as f:
                        existing_claude_response = f.readlines()
                        existing_claude_response = [json.loads(line) for line in existing_claude_response]
                        existing_claude_response = {item["asin"]: item["claude_response"] for item in existing_claude_response}
                else:
                    existing_claude_response = {}

                llm.config = config
                llm.existing_claude_response = existing_claude_response
                main(config)
