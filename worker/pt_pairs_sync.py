#!/usr/bin/env python3
"""
PT Pairs and Prompts Synchronization Service

This service reads PT pairs and prompt templates from build_prompt.py every 30 seconds
and updates them in DynamoDB for the backend APIs to consume.
"""

import boto3
import json
import time
import sys
import os
import inspect
import re
from datetime import datetime
from typing import Dict, List, Any
import logging
from botocore.exceptions import ClientError

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), "src"))

from src.build_prompt import get_comp_criterion, main_prompt
from config import AWS_REGION

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PTairsSyncService:
    def __init__(self):
        self.dynamodb = boto3.resource('dynamodb', region_name=AWS_REGION)
        self.table_name = 'auto-in-scope-pt-pairs'
        self.table = self.dynamodb.Table(self.table_name)
        
    def extract_pt_pairs_from_source(self) -> List[str]:
        """Extract all PT pairs from the get_comp_criterion function"""
        try:
            # Get the source code of the function
            source = inspect.getsource(get_comp_criterion)
            
            # Extract PT pair keys using regex pattern
            # Look for patterns like "KEY-VALUE": {
            pattern = r'"([A-Z_]+-[A-Z_]+)":\s*{'
            matches = re.findall(pattern, source)
            
            logger.info(f"Extracted {len(matches)} PT pairs from source code")
            return matches
            
        except Exception as e:
            logger.error(f"Error extracting PT pairs from source: {e}")
            return []
    
    def generate_pt_pair_data(self, pt_pair: str) -> Dict[str, Any]:
        """Generate comprehensive data for a PT pair"""
        try:
            # Get compatibility criterion
            criterion = get_comp_criterion(pt_pair)
            
            if not criterion:
                logger.warning(f"No criterion found for PT pair: {pt_pair}")
                return None
            
            # Split PT pair into components
            primary_pt, accessory_pt = pt_pair.split("-")
            
            # Generate prompt templates for both primary and accessory perspectives
            sample_item_name = "Sample Product"
            sample_bullet_points = "Sample product description with key features and specifications"
            
            # Generate primary template
            primary_template = main_prompt(
                sample_item_name,
                sample_bullet_points, 
                "primary",
                criterion,
                None  # No image for template
            )
            
            # Generate accessory template
            accessory_template = main_prompt(
                sample_item_name,
                sample_bullet_points, 
                "accessory",
                criterion,
                None  # No image for template
            )
            
            # Create comprehensive PT pair data
            pt_pair_data = {
                'pt_pair': pt_pair,
                'label': f"{primary_pt.replace('_', ' ').title()} - {accessory_pt.replace('_', ' ').title()}",
                'primary_pt': primary_pt.lower(),
                'accessory_pt': accessory_pt.lower(),
                'compatibility_criterion': criterion,
                'primary_template': primary_template,
                'accessory_template': accessory_template,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            return pt_pair_data
            
        except Exception as e:
            print(e)
            logger.error(f"Error generating data for PT pair {pt_pair}: {e}")
            return None
    
    def update_pt_pair_in_dynamodb(self, pt_pair_data: Dict[str, Any]) -> bool:
        """Update or insert PT pair data in DynamoDB"""
        try:
            response = self.table.put_item(Item=pt_pair_data)
            # logger.info(f"Updated PT pair {pt_pair_data['pt_pair']} in DynamoDB")
            return True
            
        except Exception as e:
            logger.error(f"Error updating PT pair {pt_pair_data['pt_pair']} in DynamoDB: {e}")
            return False
    
    def sync_all_pt_pairs(self) -> Dict[str, Any]:
        """Synchronize all PT pairs from source to DynamoDB"""
        logger.info("Starting PT pairs synchronization...")
        
        # Extract PT pairs from source
        pt_pairs = self.extract_pt_pairs_from_source()
        
        if not pt_pairs:
            logger.error("No PT pairs found in source code")
            return {'success': False, 'error': 'No PT pairs found'}
        
        results = {
            'success': True,
            'total_pairs': len(pt_pairs),
            'updated_pairs': 0,
            'failed_pairs': 0,
            'errors': []
        }
        
        # Process each PT pair
        for pt_pair in pt_pairs:
            try:
                # Generate PT pair data
                pt_pair_data = self.generate_pt_pair_data(pt_pair)
                
                if pt_pair_data:
                    # Update in DynamoDB
                    if self.update_pt_pair_in_dynamodb(pt_pair_data):
                        results['updated_pairs'] += 1
                    else:
                        results['failed_pairs'] += 1
                        results['errors'].append(f"Failed to update {pt_pair}")
                else:
                    results['failed_pairs'] += 1
                    results['errors'].append(f"Failed to generate data for {pt_pair}")
                    
            except Exception as e:
                logger.error(f"Error processing PT pair {pt_pair}: {e}")
                results['failed_pairs'] += 1
                results['errors'].append(f"Error processing {pt_pair}: {str(e)}")
        
        logger.info(f"Synchronization completed: {results['updated_pairs']}/{results['total_pairs']} pairs updated")
        return results
    
    def verify_table_exists(self) -> bool:
        """Verify that the DynamoDB table exists"""
        try:
            self.table.table_status
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                logger.error(f"Table {self.table_name} does not exist. Please run setup_pt_pairs_table.py first.")
                return False
            else:
                logger.error(f"Error checking table: {e}")
                return False
    
    def run_sync_loop(self, interval_seconds: int = 30):
        """Run continuous synchronization loop"""
        logger.info(f"Starting PT pairs sync service with {interval_seconds}s interval...")
        
        # Verify table exists
        if not self.verify_table_exists():
            logger.error("DynamoDB table verification failed. Exiting.")
            return
        
        while True:
            try:
                # Perform synchronization
                results = self.sync_all_pt_pairs()
                
                if results['success']:
                    logger.info(f"Sync completed successfully: {results['updated_pairs']} pairs updated")
                    if results['errors']:
                        logger.warning(f"Sync completed with {len(results['errors'])} errors: {results['errors']}")
                else:
                    logger.error(f"Sync failed: {results.get('error', 'Unknown error')}")
                
                # Wait for next sync
                logger.info(f"Waiting {interval_seconds} seconds for next sync...")
                time.sleep(interval_seconds)
                
            except KeyboardInterrupt:
                logger.info("Sync service stopped by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error in sync loop: {e}")
                logger.info(f"Retrying in {interval_seconds} seconds...")
                time.sleep(interval_seconds)

def main():
    """Main function to run the sync service"""
    sync_service = PTairsSyncService()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--once':
        # Run sync once and exit
        logger.info("Running one-time synchronization...")
        results = sync_service.sync_all_pt_pairs()
        
        if results['success']:
            print(f"✅ Synchronization completed successfully!")
            print(f"   Updated: {results['updated_pairs']}/{results['total_pairs']} PT pairs")
            if results['errors']:
                print(f"   Errors: {len(results['errors'])}")
                for error in results['errors']:
                    print(f"     - {error}")
        else:
            print(f"❌ Synchronization failed: {results.get('error', 'Unknown error')}")
            sys.exit(1)
    else:
        # Run continuous sync loop
        sync_service.run_sync_loop(30)  # 30 seconds interval

if __name__ == "__main__":
    main()