#!/usr/bin/env python3
"""
Test script for log parsing and CSV generation functionality
"""

import json
import os
import tempfile
import sys
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from task_processor import TaskProcessor

def create_test_log_file():
    """Create a test log file with sample LLM responses"""
    test_data = [
        {
            "asin": "B07XYTEST1",
            "claude_response": '{"in_scope": "yes", "reason": "This is a compatible product"}'
        },
        {
            "asin": "B07XYTEST2", 
            "claude_response": '{"in_scope": "no", "reason": "This product is not compatible"}'
        },
        {
            "asin": "B07XYTEST3",
            "claude_response": "error"
        },
        {
            "asin": "B07XYTEST4",
            "claude_response": "no output"
        },
        {
            "asin": "B07XYTEST5",
            "claude_response": '{"in_scope": "yes", "reason": "Perfect match for compatibility criteria"}'
        },
        {
            "asin": "B07XYTEST6",
            "claude_response": 'Invalid JSON response without proper format'
        },
        {
            "asin": "B07XYTEST7",
            "claude_response": '{"in_scope": "unknown", "reason": "Insufficient information to determine compatibility"}'
        }
    ]
    
    # Create temporary log file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False, encoding='utf-8') as log_file:
        for entry in test_data:
            log_file.write(json.dumps(entry) + '\n')
        
        # Add some invalid lines to test error handling
        log_file.write('Invalid JSON line\n')
        log_file.write('{"asin": "incomplete"}\n')  # Missing claude_response
        
        return log_file.name

def test_log_parsing():
    """Test the log parsing functionality"""
    print("=== Testing Log Parsing and CSV Generation ===\n")
    
    # Create test log file
    test_log_path = create_test_log_file()
    print(f"Created test log file: {test_log_path}")
    
    # Create test directory
    test_dir = tempfile.mkdtemp()
    print(f"Using test directory: {test_dir}")
    
    # Create task processor instance
    processor = TaskProcessor()
    
    # Create test config
    test_config = {
        'pt_pair': 'printer-laser_printer_toner',
        'primary_pt': 'printer',
        'accessory_pt': 'laser_printer_toner',
        'acc_pri': 'primary',
        'current_pt': 'printer'
    }
    
    try:
        # Test the log to CSV conversion
        print("\\nTesting log to CSV conversion...")
        csv_path = processor.convert_log_to_csv(test_log_path, test_dir, test_config)
        
        if csv_path and os.path.exists(csv_path):
            print(f"✅ Successfully created CSV file: {csv_path}")
            
            # Read and display the CSV content
            import pandas as pd
            df = pd.read_csv(csv_path)
            
            print(f"\\n📊 CSV Content Summary:")
            print(f"   Total rows: {len(df)}")
            print(f"   Columns: {list(df.columns)}")
            
            # Show in_scope distribution
            in_scope_counts = df['in_scope'].value_counts()
            print(f"\\n📈 In Scope Distribution:")
            for status, count in in_scope_counts.items():
                print(f"   {status}: {count}")
            
            # Show first few rows
            print(f"\\n📋 First 3 rows:")
            print(df.head(3).to_string(index=False))
            
            # Test individual parsing function
            print(f"\\n🔍 Testing individual response parsing:")
            test_responses = [
                '{"in_scope": "yes", "reason": "Compatible"}',
                '{"in_scope": "no", "reason": "Not compatible"}',
                'error',
                'Invalid JSON',
                '{"in_scope": "unknown"}'  # Missing reason
            ]
            
            for i, response in enumerate(test_responses, 1):
                parsed = processor.parse_claude_response(response)
                print(f"   Test {i}: {response[:30]}...")
                print(f"      -> in_scope: {parsed['in_scope']}, reason: {parsed['reason'][:50]}...")
            
            print(f"\\n✅ All tests completed successfully!")
            return True
            
        else:
            print(f"❌ Failed to create CSV file")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        try:
            if os.path.exists(test_log_path):
                os.unlink(test_log_path)
            if csv_path and os.path.exists(csv_path):
                os.unlink(csv_path)
            if os.path.exists(test_dir):
                import shutil
                shutil.rmtree(test_dir)
            print(f"\\n🧹 Cleaned up test files")
        except Exception as e:
            print(f"⚠️  Warning: Cleanup failed: {e}")

def test_parse_claude_response():
    """Test the Claude response parsing function in isolation"""
    print("\\n=== Testing Claude Response Parsing ===\\n")
    
    processor = TaskProcessor()
    
    test_cases = [
        # Valid JSON responses
        ('{"in_scope": "yes", "reason": "Product is compatible"}', "yes", "Product is compatible"),
        ('{"in_scope": "no", "reason": "Not compatible with criteria"}', "no", "Not compatible with criteria"),
        ('{"in_scope": "unknown", "reason": "Insufficient information"}', "unknown", "Insufficient information"),
        
        # Edge cases
        ('{"in_scope": "yes"}', "yes", "No reason provided"),  # Missing reason
        ('{"reason": "Some reason"}', "unknown", "No reason provided"),  # Missing in_scope
        ('error', "unknown", "error"),  # Error response
        ('no output', "unknown", "no output"),  # No output response
        
        # Invalid JSON
        ('Invalid JSON string', "unknown", "Failed to parse JSON response"),
        ('{"in_scope": "yes", "reason":}', "unknown", "Parse error"),  # Malformed JSON
        ('', "unknown", "Failed to parse JSON response"),  # Empty response
    ]
    
    success_count = 0
    
    for i, (input_response, expected_in_scope, expected_reason_contains) in enumerate(test_cases, 1):
        try:
            result = processor.parse_claude_response(input_response)
            
            # Check if the result matches expectations
            in_scope_match = result['in_scope'] == expected_in_scope
            reason_match = expected_reason_contains.lower() in result['reason'].lower()
            
            status = "✅" if (in_scope_match and reason_match) else "❌"
            
            print(f"{status} Test {i:2d}: {input_response[:40]:<40}")
            print(f"         Expected: in_scope='{expected_in_scope}', reason contains '{expected_reason_contains}'")
            print(f"         Got:      in_scope='{result['in_scope']}', reason='{result['reason'][:50]}...'")
            print()
            
            if in_scope_match and reason_match:
                success_count += 1
                
        except Exception as e:
            print(f"❌ Test {i:2d}: Exception occurred - {str(e)}")
            print()
    
    print(f"📊 Results: {success_count}/{len(test_cases)} tests passed")
    return success_count == len(test_cases)

if __name__ == "__main__":
    print("Starting log parsing tests...")
    print("=" * 60)
    
    # Test individual parsing function
    parse_success = test_parse_claude_response()
    
    # Test full log to CSV conversion
    conversion_success = test_log_parsing()
    
    print("\\n" + "=" * 60)
    if parse_success and conversion_success:
        print("🎉 All tests passed successfully!")
    else:
        print("❌ Some tests failed. Please check the output above.")
        sys.exit(1)