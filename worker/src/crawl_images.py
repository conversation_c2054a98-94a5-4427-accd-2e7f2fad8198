import requests
import traceback
import concurrent.futures
import os
import sys
import warnings
import pandas as pd
from tqdm import tqdm
import boto3
import io
import argparse
import time
import json
from datetime import datetime

BASE_DIR = os.path.dirname(os.path.abspath('{}/../'.format(__file__)))
sys.path.append(BASE_DIR)

from src.utils import read_input_data

warnings.filterwarnings('ignore')

mp_id_map = {
    'jp': '6',
    'tr': '338851',
    'uk': '3',
    'us': '1',
    'de': '4',
    'fr': '5',
    'es': '44551',
    'it': '35691',
    'in': '44571',
    'ca': '7',
    'au': '111172',
    'mx': '771770',
    'cn': '3240',
    'br': '526970',
}

mp_sable_map = {
    'jp': 'http://sable-responders-adhoc-pdx.amazon.com',
    'tr': 'http://sable-responders-adhoc-dub.amazon.com',
    'uk': 'http://sable-responders-adhoc-dub.amazon.com',
    'us': 'http://sable-responders-adhoc-iad.iad.proxy.amazon.com',
    'de': 'http://sable-responders-adhoc-dub.amazon.com',
    'fr': 'http://sable-responders-adhoc-dub.amazon.com',
    'es': 'http://sable-responders-adhoc-dub.amazon.com',
    'it': 'http://sable-responders-adhoc-dub.amazon.com',
    'in': 'http://sable-responders-adhoc-dub.amazon.com',
    'ca': 'http://sable-responders-adhoc-iad.iad.proxy.amazon.com',
    'au': 'http://sable-responders-adhoc-pdx.amazon.com',
    'mx': 'http://sable-responders-adhoc-iad.iad.proxy.amazonvim .com',
    'cn': 'http://sable-responders-adhoc-pek.amazon.com',
    'br': 'http://sable-responders-adhoc-iad.iad.proxy.amazon.com',
    'uae': ''
}

# product_type = 'inkjet_printer'
# marketplace = 'de'
session = boto3.Session(profile_name="beta", region_name="us-east-1")
s3_client = session.client('s3', region_name="us-east-1")
dynamodb = boto3.resource('dynamodb')
BUCKET = "ude-image-extraction"
DYNAMODB_TABLE_NAME = "crawl_tasks"


def requstes_no_session_result(url, way, headers=None, data=None, files=None, cookies=None, retry=5,
                               error_reason=None):
    if retry <= 0:
        return error_reason

    try:
        if way == 'post':
            res = requests.post(url, headers=headers, data=data, files=files, cookies=cookies, timeout=10, verify=False)
        else:
            res = requests.get(url, headers=headers, timeout=10, cookies=cookies, verify=False)
        if res.status_code > 300:
            return requstes_no_session_result(url, way, headers=headers, data=data, cookies=cookies,
                                              retry=retry - 1, error_reason=res)
        else:
            return res
    except:
        return requstes_no_session_result(url, way, headers=headers, data=data, cookies=cookies, retry=retry - 1,
                                          error_reason=None)


def get_scale_img(asin, mp):
    # if not os.path.exists("{}/images/{}".format(BASE_DIR, asin)):
    #     # if not, then create it
    #     os.mkdir("{}/images/{}".format(BASE_DIR, asin))
    try:
        mp = str(mp)
        # url = mp_dp_map.get(mp.lower(), mp_dp_map['us']) + asin
        url = f"{mp_sable_map.get(mp.lower(), mp_sable_map['us'])}/datapath/query/rankedmedia/rankedImagesV2/-/{mp_id_map.get(mp.lower(), '1')}/{asin}/?isHighRes=1&maxImages=0"
        # print(url)
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Encoding': 'gzip, deflate',
            'Accept': 'application/json',
            'Connection': 'keep-alive'}
        res = requstes_no_session_result(url, 'get', headers=headers)
        try:
            images = res.json()
        except:
            images = {}
        if images.get('merchantImages', []).__len__() >= 1:
            images = [[each['large']['physicalID'], each['large']['variant'], each['large']['extension']] for each in
                      images['merchantImages'] if each['large']['variant'] == 'MAIN']
            return images
        else:
            # print(f'{asin} don\'t have img')
            return []
    except Exception as e:
        traceback.print_exc()
        print(f'{asin} image download error: {e}')
        return []


def upload_file(asin, mp, current_pt, acc_pri):
    images = get_scale_img(asin, mp)
    for image in images:
        try:
            image_url = f"https://m.media-amazon.com/images/I/{image[0]}.{image[-1]}"
            obj_path = f"in_scope/{current_pt}_{acc_pri}/images/{mp}/{asin}.{image[-1]}"
            image_res = requstes_no_session_result(image_url, 'get')
            s3_client.upload_fileobj(io.BytesIO(image_res.content), "compatibility-data-gen", obj_path)

        except Exception as ex:
            print(ex)

    return True


def download_file_from_s3(s3_path, local_path):
    try:
        bucket_name, key = s3_path.replace('s3://', '').split('/', 1)
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        s3_client.download_file(bucket_name, key, local_path)
        print(f"Downloaded {s3_path} to {local_path}")
        return True
    except Exception as e:
        print(f"Error downloading file from S3: {e}")
        return False


def update_record_status(table, record_id, status):
    try:
        table.update_item(
            Key={'id': record_id},
            UpdateExpression='SET #status = :status, updated_at = :timestamp',
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':status': status,
                ':timestamp': datetime.utcnow().isoformat()
            }
        )
        print(f"Updated record {record_id} status to {status}")
        return True
    except Exception as e:
        print(f"Error updating record status: {e}")
        return False


def process_crawl_task(record):
    try:
        record_id = record['id']
        s3_path = record['s3_path']
        
        filename = os.path.basename(s3_path)
        local_file_path = os.path.join(BASE_DIR, 'data', filename)
        
        if download_file_from_s3(s3_path, local_file_path):
            if local_file_path.endswith('.xlsx'):
                data = pd.read_excel(local_file_path)
            elif local_file_path.endswith('.tsv') or local_file_path.endswith('.tsv000'):
                data = read_input_data(local_file_path)
            else:
                print(f"Unsupported file format: {local_file_path}")
                return False
            
            if len(data) == 0:
                print(f"No data found in {local_file_path}")
                return False
            
            pt_pair = record.get('pt_pair', 'notebook_computer-monitor')
            current_pt = record.get('current_pt', 'monitor')
            acc_pri = record.get('acc_pri', 'accessory')
            mp = record.get('mp', 'us')
            
            asins = data['asin'].tolist()
            asins = [(asin, mp) for asin in asins]

            with concurrent.futures.ProcessPoolExecutor(max_workers=20) as executor, tqdm(total=len(asins),
                                                                                            desc=f'Processing {record_id}') as bar:
                future_to_complete = {
                    executor.submit(upload_file, asin_info[0], asin_info[1], current_pt, acc_pri): asin_info
                    for asin_info in asins}
                for future in concurrent.futures.as_completed(future_to_complete):
                    result = future.result()
                    bar.update(1)
            
            return True
        return False
    except Exception as e:
        print(f"Error processing crawl task: {e}")
        traceback.print_exc()
        return False


def listen_dynamodb_table():
    table = dynamodb.Table(DYNAMODB_TABLE_NAME)
    
    print(f"Starting DynamoDB listener for table: {DYNAMODB_TABLE_NAME}")
    
    while True:
        try:
            response = table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('status').eq('waiting')
            )
            
            waiting_records = response['Items']
            
            if waiting_records:
                print(f"Found {len(waiting_records)} waiting records")
                
                for record in waiting_records:
                    record_id = record['id']
                    print(f"Processing record: {record_id}")
                    
                    update_record_status(table, record_id, 'processing')
                    
                    success = process_crawl_task(record)
                    
                    if success:
                        update_record_status(table, record_id, 'completed')
                        print(f"Successfully processed record: {record_id}")
                    else:
                        update_record_status(table, record_id, 'failed')
                        print(f"Failed to process record: {record_id}")
            
            time.sleep(10)
            
        except KeyboardInterrupt:
            print("Stopping DynamoDB listener...")
            break
        except Exception as e:
            print(f"Error in DynamoDB listener: {e}")
            traceback.print_exc()
            time.sleep(30)


def main(mp='us', pt_pair='notebook_computer-monitor', current_pt='monitor', acc_pri='accessory'):
    if os.path.exists(f"{BASE_DIR}/data/{pt_pair}/{current_pt}_{acc_pri}_pt_data_{mp_id_map[mp]}.tsv000"):
        data = read_input_data(f"{BASE_DIR}/data/{pt_pair}/{current_pt}_{acc_pri}_pt_data_{mp_id_map[mp]}.tsv000")
    else:
        data = pd.read_excel(f"{BASE_DIR}/data/{pt_pair}/{current_pt}_{acc_pri}_pt_data_.xlsx")
        
    if len(data) == 0:
        print(f"no data for {current_pt}")
        return
    
    asins = data['asin'].tolist()
    asins = [(asin, mp) for asin in asins]

    with concurrent.futures.ProcessPoolExecutor(max_workers=20) as executor, tqdm(total=len(asins),
                                                                                    desc='Getting Image') as bar:
        future_to_complete = {
            executor.submit(upload_file, asin_info[0], asin_info[1], current_pt, acc_pri): asin_info
            for asin_info in asins}
        for future in concurrent.futures.as_completed(future_to_complete):
            result = future.result()
            bar.update(1)
            # if result:
            #     results.append(result)
    return


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Image Crawler with DynamoDB Listener')
    parser.add_argument('--listen', action='store_true', help='Start DynamoDB listener mode')
    parser.add_argument('--table-name', default='crawl_tasks', help='DynamoDB table name')
    args = parser.parse_args()
    
    if args.listen:
        DYNAMODB_TABLE_NAME = args.table_name
        listen_dynamodb_table()
    else:
        # pt_pairs = ["CAMERA_FILM-PHOTOGRAPHIC_FILM", "CAMCORDER-CAMERA_ENCLOSURE", "CAMCORDER-CAMERA_SUPPORT", "CAMCORDER-CAMERA_LENS_FILTERS", "CAMERA_DIGITAL-CAMERA_ENCLOSURE", "CAMCORDER-FLASH_MEMORY", "CAMERA_DIGITAL-FLASH_MEMORY", "CAMERA_DIGITAL-CAMERA_LENS_FILTERS", "CELLULAR_PHONE-FLASH_MEMORY", "CELLULAR_PHONE-HEADPHONES", "GPS_OR_NAVIGATION_SYSTEM-FLASH_MEMORY", "SECURITY_CAMERA-FLASH_MEMORY", "NOTEBOOK_COMPUTER-COMPUTER_DRIVE_OR_STORAGE", "NOTEBOOK_COMPUTER-FLASH_MEMORY", "PERSONAL_COMPUTER-COMPUTER_DRIVE_OR_STORAGE", "PERSONAL_COMPUTER-FLASH_MEMORY", "VIDEO_GAME_CONSOLE-FLASH_MEMORY", "VIDEO_GAME_CONSOLE-HEADPHONES", "CELLULAR_PHONE_CASE-CELL_PHONE_GRIP", "CALCULATOR-BATTERY", "CLOCK-BATTERY", "ELECTRIC_CIRCUIT_TESTING_DEVICE-BATTERY", "FLASHLIGHT-BATTERY", "RADIO-BATTERY", "REMOTE_CONTROL-BATTERY", "MANUAL_SHAVING_RAZOR-RAZOR_BLADE_CARTRIDGE"]
        pt_pairs = ["INFANT_TODDLER_CAR_SEAT-STROLLER"]
        acc_memo = set()
        pri_memo = set()
        for pt_pair in tqdm(pt_pairs):
            pt_pair = pt_pair.lower()
            print(f"now the pt_pair {pt_pair} is being processed")
            primary_pt = pt_pair.split('-')[0].lower()
            accessory_pt = pt_pair.split('-')[1].lower()
            for current_pt in [accessory_pt, primary_pt]:
                if current_pt == primary_pt:
                    acc_pri = "primary"
                    memo = pri_memo
                else:
                    acc_pri = "accessory"
                    memo = acc_memo

                if current_pt in memo:
                    print(f"skip {current_pt}")
                    continue
                else:
                    memo.add(accessory_pt)
            
                for mp in ['us', 'uk', 'de', 'fr', 'es', 'it','ca']:
                    main(mp=mp, pt_pair=pt_pair, current_pt=current_pt, acc_pri=acc_pri)

