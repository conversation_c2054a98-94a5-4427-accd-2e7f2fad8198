#!/usr/bin/env python3
import boto3
import json
import time
import os
import sys
import uuid
import pandas as pd
import re
from tqdm import tqdm
BASE_DIR = os.path.dirname(os.path.abspath('{}/../'.format(__file__)))
sys.path.append(BASE_DIR)

from src.utils import get_main_image, read_input_data, layer_sample
from src.build_prompt import main_prompt


class BedrockBatchInference:
    """AWS Bedrock批量推理工具类"""
    
    def __init__(self, config, region_name='us-west-2', role_arn=None):
        """
        初始化Bedrock批量推理类
        
        Args:
            region_name (str): AWS区域名称
            role_arn (str): 用于批量作业的IAM角色ARN
        """
        self.config = config
        self.region_name = region_name
        self.role_arn = role_arn or "arn:aws:iam::043594468649:role/service-role/rrocwang-batch-inference"
        self.bedrock_client = self._get_bedrock_client()
        self.bedrock_runtime_client = self._get_bedrock_runtime_client()
        self.s3_client = boto3.client('s3')
    
    def _get_bedrock_client(self):
        """获取AWS Bedrock客户端"""
        return boto3.client(
            service_name='bedrock',
            region_name=self.region_name,
        )
    
    def _get_bedrock_runtime_client(self):
        """获取AWS Bedrock Runtime客户端"""
        return boto3.client(
            service_name='bedrock-runtime',
            region_name=self.region_name,
        )
    
    def prepare_input_file(self, input_data, output_path):
        """
        准备批量推理的输入文件
        
        Args:
            input_data (list): 输入数据列表，每项需包含"prompt"字段
            output_path (str): 输出文件路径
            
        Returns:
            str: 创建的输入文件路径
        """
        current_pt = self.config['current_pt']
        primary_pt = self.config['primary_pt']
        accessory_pt = self.config['accessory_pt']
        compatibility_criterion = self.config['compatibility_criterion']

        def get_prompt(row):
            image_base64 = get_main_image(row['product_type'].lower(), row['asin'])
                
            item_name = row['item_name']
            bullet_point_list = []
            for i in range(1, 6):
                if pd.isna(row[f'bullet_point{i}']) or str(row[f'bullet_point{i}']).strip() == "":
                    continue
                bullet_point_list.append(row[f'bullet_point{i}'])
            bullet_points = "\n".join(bullet_point_list)

            product_category_a = re.sub(r'_', ' ', current_pt)
            product_category_b = re.sub(r'_', ' ', primary_pt if current_pt == accessory_pt else accessory_pt)
            prompt = main_prompt(item_name, bullet_points, product_category_a, product_category_b, compatibility_criterion, image_base64)

            # 为Claude构建正确的输入格式
            if image_base64 is not None:
                claude_input = {"recordId": row['asin'], "modelInput": {"anthropic_version": "bedrock-2023-05-31", "max_tokens": 1024, "temperature": 0, "messages": [{"role": "user", "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/jpeg",
                                "data": image_base64
                            }
                        }
                    ]}]}}
            else:
                claude_input = {"recordId": row['asin'], "modelInput": {"anthropic_version": "bedrock-2023-05-31", "max_tokens": 1024, "temperature": 0, "messages": [{"role": "user", "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]}]}}
            
            return claude_input

        tqdm.pandas()
        input_data['claude_input'] = input_data.progress_apply(get_prompt, axis=1)
        with open(output_path, 'w', encoding='utf-8') as f:
            for index, row in tqdm(input_data.iterrows()):
                f.write(json.dumps(row['claude_input']) + '\n')
        
        return output_path
    
    def setup_batch_job(self, model_id, input_file_path, output_s3_uri):
        """
        设置批量推理作业
        
        Args:
            model_id (str): 模型ID，如"anthropic.claude-3-sonnet-20240229"
            input_file_path (str): 输入文件路径
            output_s3_uri (str): 输出结果的S3 URI
            
        Returns:
            str: 创建的批量作业ARN
        """
        # 创建S3输入数据位置
        bucket_name = "rrocwang-batch-inference"
        input_s3_prefix = f"in_scope/{pt_pair}"
        input_s3_uri = f"s3://{bucket_name}/{input_s3_prefix}/"
        
        # 上传输入文件到S3
        input_filename = os.path.basename(input_file_path)
        s3_input_key = f"{input_s3_prefix}/{input_filename}"
        self.s3_client.upload_file(input_file_path, bucket_name, s3_input_key)
        
        # 创建批量推理作业
        response = self.bedrock_client.create_model_invocation_job(
            modelId=model_id,
            jobName=f"inscope-job-{str(uuid.uuid4())[:3]}",
            roleArn=self.role_arn,
            inputDataConfig={
                "s3InputDataConfig": {
                    "s3Uri": input_s3_uri,
                    "s3InputFormat": "JSONL"
                }
            },
            outputDataConfig={
                "s3OutputDataConfig": {
                    "s3Uri": output_s3_uri
                }
            },
            clientRequestToken=str(uuid.uuid4())
        )
        
        return response['jobArn']
    
    def check_job_status(self, job_arn):
        """
        检查批量推理作业状态
        
        Args:
            job_arn (str): 批量作业ARN
            
        Returns:
            tuple: (状态, 作业详情响应)
        """
        while True:
            response = self.bedrock_client.get_model_invocation_job(
                jobIdentifier=job_arn
            )
            
            status = response['status']
            print(f"当前作业状态: {status}")
            
            if status in ['COMPLETED', 'FAILED', 'STOPPING', 'STOPPED']:
                return status, response
            
            # 每30秒检查一次状态
            time.sleep(15)
    
    def read_output_results(self, output_s3_uri):
        """
        读取并解析输出结果
        
        Args:
            output_s3_uri (str): 输出结果的S3 URI
            
        Returns:
            list: 解析后的结果列表
        """
        bucket_name = output_s3_uri.split('/')[2]
        prefix = '/'.join(output_s3_uri.split('/')[3:])
        
        # 列出输出文件
        response = self.s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=prefix
        )
        
        results = []
        for obj in response.get('Contents', []):
            if obj['Key'].endswith('.out'):
                # 下载输出文件
                local_file = f"/tmp/{os.path.basename(obj['Key'])}"
                self.s3_client.download_file(bucket_name, obj['Key'], local_file)
                
                # 读取结果
                with open(local_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        results.append(json.loads(line))
        
        return results
    
    def run_batch_inference(self, model_id, input_data, output_s3_uri, input_file_path=None):
        """
        运行完整的批量推理流程
        
        Args:
            model_id (str): 模型ID
            input_data (list): 输入数据列表
            output_s3_uri (str): 输出结果的S3 URI
            input_file_path (str, optional): 输入文件路径，若为None则自动生成
            
        Returns:
            tuple: (状态, 结果列表, 作业详情)
        """
        # 准备输入文件
        if input_file_path is None:
            input_file_path = f"{BASE_DIR}/data/{pt_pair}/{current_pt}_{acc_pri}_prompt_1.jsonl"
        
        if not os.path.exists(input_file_path):
            input_file = self.prepare_input_file(input_data, input_file_path)
            print(f"已创建输入文件: {input_file}")
        else:
            input_file = input_file_path
            print(f"输入文件已存在: {input_file_path}")
        
        # 启动批量推理作业
        job_arn = self.setup_batch_job(model_id, input_file, output_s3_uri)
        print(f"已创建批量推理作业: {job_arn}")
        
        # 检查作业状态
        status, job_details = self.check_job_status(job_arn)
        print(f"作业完成状态: {status}")
        
        # 如果作业完成，读取结果
        results = []
        if status == 'COMPLETED':
            results = self.read_output_results(output_s3_uri)
        
        return status, results, job_details


def main():
    # 配置参数
    # MODEL_ID = "anthropic.claude-3-5-sonnet-20241022-v2:0"
    MODEL_ID = "anthropic.claude-3-5-sonnet-20240620-v1:0"
    OUTPUT_S3_URI = f"s3://rrocwang-batch-inference/in_scope/{pt_pair}/"
    
    # 示例输入数据
    data_df = read_input_data(f"{BASE_DIR}/data/{pt_pair}/{current_pt}_{acc_pri}_pt_data_1.tsv000")
    print(f"数据集大小: {len(data_df)}")
    if len(data_df) > 20000:
        data_df = layer_sample(data_df)

    print(f"采样后数据集大小: {len(data_df)}")
    
    # 创建批量推理客户端
    bedrock_batch = BedrockBatchInference(
        config=config,
        region_name="us-west-2",
        role_arn="arn:aws:iam::043594468649:role/service-role/rrocwang-batch-inference"
    )
    
    # 运行批量推理
    status, results, job_details = bedrock_batch.run_batch_inference(
        model_id=MODEL_ID,
        input_data=data_df,
        output_s3_uri=OUTPUT_S3_URI
    )
    
    # 处理结果
    if status == 'COMPLETED':
        print(f"作业完成，结果: {results}")
    else:
        print(f"作业未成功完成，详情: {job_details}")


if __name__ == "__main__":
    pt_pair = "notebook_computer-monitor"
    primary_pt = pt_pair.split('-')[0]
    accessory_pt = pt_pair.split('-')[1]
    current_pt = accessory_pt
    date = "20240601"

    compatibility_criterion = "connectivity technology. e.g. USB, HDMI, DP, etc."

    if current_pt == primary_pt:
        acc_pri = "primary"
    else:
        acc_pri = "accessory"

    config = {
        "pt_pair": pt_pair,
        "primary_pt": primary_pt,
        "accessory_pt": accessory_pt,
        "current_pt": current_pt,
        "acc_pri": acc_pri,
        "date": date,
        "compatibility_criterion": compatibility_criterion,
    }
    main()
