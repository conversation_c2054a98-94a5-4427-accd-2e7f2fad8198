import boto3
import json
import os
import sys
import pandas as pd
import tempfile
from datetime import datetime
from typing import Dict, Any, List
import logging
import concurrent.futures
import traceback
import re
from tqdm import tqdm
import zipfile
import time
import threading
import glob

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), "src"))

from src.call_llm import LLM
from src.build_prompt import get_comp_criterion
from src.crawl_images import upload_file
from config import *

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TaskProcessor:
    def __init__(self):
        # Use default AWS credentials instead of specific profile
        self.dynamodb = boto3.resource('dynamodb', region_name=AWS_REGION)
        self.s3_client = boto3.client('s3', region_name=AWS_REGION)
        self.task_table = self.dynamodb.Table(AWS_TASK_TABLE)

        session = boto3.Session(profile_name="beta", region_name="us-east-1")
        self.s3_client_beta = session.client('s3', region_name="us-east-1")
        
    def update_task_status(self, task_id: str, status: str, **kwargs) -> bool:
        """Update task status in DynamoDB"""
        try:
            update_expression = "SET #status = :status, updated_at = :timestamp"
            expression_attribute_names = {'#status': 'status'}
            expression_attribute_values = {
                ':status': status,
                ':timestamp': datetime.now().isoformat()
            }
            
            # Add optional fields
            for key, value in kwargs.items():
                if value is not None:
                    update_expression += f", {key} = :{key}"
                    expression_attribute_values[f":{key}"] = value
            
            if status == "completed":
                update_expression += ", completed_at = :completed"
                expression_attribute_values[':completed'] = datetime.now().isoformat()
            
            self.task_table.update_item(
                Key={'task_id': task_id},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_attribute_names,
                ExpressionAttributeValues=expression_attribute_values
            )
            
            logger.info(f"Task {task_id} status updated to {status}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {str(e)}")
            return False
    
    def get_task(self, task_id: str) -> Dict[str, Any]:
        """Get task details from DynamoDB"""
        try:
            response = self.task_table.get_item(Key={'task_id': task_id})
            return response.get('Item')
        except Exception as e:
            logger.error(f"Error getting task {task_id}: {str(e)}")
            return None
    
    def download_and_extract_files(self, s3_uri: str, task_id: str) -> str:
        """Download file from S3 and extract if needed"""
        bucket_name, key = s3_uri.replace('s3://', '').split('/', 1)
        
        # Create task directory
        task_dir = os.path.join('./data', task_id)
        os.makedirs(task_dir, exist_ok=True)
        
        # Download file
        filename = os.path.basename(key)
        local_file_path = os.path.join(task_dir, filename)
        
        self.s3_client.download_file(bucket_name, key, local_file_path)
        logger.info(f"Downloaded file to {local_file_path}")
        
        # Extract if zip file
        if filename.lower().endswith('.zip'):
            with zipfile.ZipFile(local_file_path, 'r') as zip_ref:
                zip_ref.extractall(task_dir)
            logger.info(f"Extracted zip file to {task_dir}")
            os.remove(local_file_path)  # Remove zip file after extraction
        
        return task_dir
    
    def find_data_files(self, task_dir: str) -> List[Dict[str, Any]]:
        """Find and categorize data files based on naming convention"""
        data_files = []
        
        # Look for files with specific patterns
        extensions = ['*.xlsx', '*.csv', '*.tsv', '*.tsv000']
        
        for ext in extensions:
            pattern = os.path.join(task_dir, '**', ext)
            files = glob.glob(pattern, recursive=True)
            
            for file_path in files:
                filename = os.path.basename(file_path)
                
                # Check if filename matches the pattern: primary_pt_accessory_pt_(primary/accessory)_mp.suffix
                if '_' in filename:
                    parts = filename.split('_')
                    if len(parts) >= 4:
                        # Determine acc_pri based on filename
                        if 'primary' in filename.lower():
                            acc_pri = 'primary'
                        elif 'accessory' in filename.lower():
                            acc_pri = 'accessory'
                        else:
                            acc_pri = 'accessory'  # default
                        
                        data_files.append({
                            'file_path': file_path,
                            'filename': filename,
                            'acc_pri': acc_pri,
                            "mp": parts[-1]
                        })
                    else:
                        # Fallback: assume accessory if pattern doesn't match
                        data_files.append({
                            'file_path': file_path,
                            'filename': filename,
                            'acc_pri': 'accessory',
                            "mp": parts[-1]
                        })
        
        logger.info(f"Found {len(data_files)} data files: {[f['filename'] for f in data_files]}")
        return data_files
    
    def read_data_file(self, file_path: str) -> pd.DataFrame:
        """Read a single data file"""
        try:
            if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                data_df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                data_df = pd.read_csv(file_path, encoding='utf8', dtype='str', escapechar='\\', on_bad_lines='skip')
            else:  # .tsv or .tsv000
                data_df = pd.read_csv(file_path, sep='\t', encoding='utf8', dtype='str', escapechar='\\', on_bad_lines='skip', quoting=3)
            
            logger.info(f"Successfully read {len(data_df)} rows from {file_path}")
            return data_df
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {str(e)}")
            return pd.DataFrame()
    
    def in_scope_extract(self, claude_output):
        """Extract in_scope result from Claude output"""
        try:
            if claude_output == "no output":
                return "no output"
            claude_output = re.search(r"\{.*?\}", claude_output).group(0)
            in_scope_res = json.loads(claude_output)["in_scope"]
        except Exception:
            in_scope_res = "unknown"
        return in_scope_res
    
    def process_images(self, data_df: pd.DataFrame, config: Dict[str, Any], task_id: str):
        """Process images in parallel"""
        asins = data_df['asin'].tolist()
        mp = config['mp']
        current_pt = config['current_pt']
        acc_pri = config['acc_pri']
        
        logger.info(f"Starting image processing for {len(asins)} ASINs")
        
        with concurrent.futures.ProcessPoolExecutor(max_workers=20) as executor, \
             tqdm(total=len(asins), desc='Processing Images') as pbar:
            
            future_to_asin = {
                executor.submit(upload_file, asin, mp, current_pt, acc_pri): asin 
                for asin in asins
            }
            
            for future in concurrent.futures.as_completed(future_to_asin):
                asin = future_to_asin[future]
                try:
                    future.result()  # Wait for completion
                    pbar.update(1)
                except Exception as e:
                    logger.error(f"Error processing image for ASIN {asin}: {str(e)}")
                    pbar.update(1)
        
        logger.info("Image processing completed")
        self.update_task_status(task_id, "processing", progress=30)
    
    def process_with_llm_parallel(self, data_df: pd.DataFrame, config: Dict[str, Any], task_id: str, task_dir: str, model_name: str = None) -> List[Dict[str, Any]]:
        """Process data with LLM using parallel execution and log results"""
        logger.info(f"Starting parallel LLM processing with model: {model_name or DEFAULT_MODEL}")
        
        # Initialize LLM with specified model
        llm = LLM(config=config, existing_claude_response={}, model_name=model_name or DEFAULT_MODEL)
        
        # Set up log file for current_pt
        current_pt = config['current_pt']
        log_file_path = os.path.join(task_dir, f"{current_pt}.log")
        
        results = []
        total_items = len(data_df)
        processed = 0
        
        # Use ThreadPoolExecutor similar to call_llm.py
        max_clients = min(len(llm.clients), total_items)
        data_list = data_df.to_dict(orient='records')
        
        with open(log_file_path, 'a', encoding='utf-8') as log_file:
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_clients) as executor:
                # Submit all tasks
                future_to_data = {}
                data_gen = iter(data_list)
                
                # Initialize with first batch
                for i in range(min(max_clients, total_items)):
                    try:
                        row_data = next(data_gen)
                        future = executor.submit(llm.call_claude, i % max_clients, row_data)
                        future_to_data[future] = row_data
                    except StopIteration:
                        break
                
                # Process completed futures and submit new ones
                while future_to_data:
                    done, _ = concurrent.futures.wait(future_to_data, return_when=concurrent.futures.FIRST_COMPLETED)
                    
                    for future in done:
                        row_data = future_to_data.pop(future)
                        
                        try:
                            result = future.result()
                            results.append(result)
                            processed += 1
                            
                            # Log result to file
                            log_file.write(json.dumps(result) + '\n')
                            log_file.flush()  # Ensure immediate write
                            
                            # Update progress every 5 items
                            if processed % 5 == 0:
                                progress = 30 + int((processed / total_items) * 60)  # 30-90% range
                                self.update_task_status(
                                    task_id, 
                                    "processing",
                                    progress=progress,
                                    processed_items=processed
                                )
                            
                            # Submit next task if available
                            try:
                                next_row = next(data_gen)
                                client_id = processed % max_clients
                                new_future = executor.submit(llm.call_claude, client_id, next_row)
                                future_to_data[new_future] = next_row
                            except StopIteration:
                                pass
                                
                        except Exception as e:
                            logger.error(f"Error processing ASIN {row_data.get('asin', 'unknown')}: {str(e)}")
                            error_result = {
                                "asin": row_data.get('asin', 'unknown'), 
                                "claude_response": "error"
                            }
                            results.append(error_result)
                            
                            # Log error result to file
                            log_file.write(json.dumps(error_result) + '\n')
                            log_file.flush()
                            
                            processed += 1
                            
                            # Submit next task even on error
                            try:
                                next_row = next(data_gen)
                                client_id = processed % max_clients
                                new_future = executor.submit(llm.call_claude, client_id, next_row)
                                future_to_data[new_future] = next_row
                            except StopIteration:
                                pass
        
        logger.info(f"Parallel LLM processing completed. Processed {processed} items. Results logged to {log_file_path}")
        return results
    
    def parse_claude_response(self, claude_response: str) -> Dict[str, str]:
        """Parse Claude's JSON response into structured data"""
        try:
            if claude_response == "error" or claude_response == "no output":
                return {
                    "in_scope": "unknown",
                    "reason": claude_response
                }
            
            # Try to extract JSON from the response
            claude_response_clean = re.search(r"\{.*?\}", claude_response)
            if claude_response_clean:
                response_json = json.loads(claude_response_clean.group(0))
                return {
                    "in_scope": response_json.get("in_scope", "unknown"),
                    "reason": response_json.get("reason", "No reason provided")
                }
            else:
                return {
                    "in_scope": "unknown",
                    "reason": "Failed to parse JSON response"
                }
        except Exception as e:
            logger.warning(f"Error parsing Claude response: {str(e)}")
            return {
                "in_scope": "unknown",
                "reason": f"Parse error: {str(e)}"
            }
    
    def convert_log_to_csv(self, log_file_path: str, task_dir: str, config: Dict[str, Any]) -> str:
        """Convert log file to structured CSV with parsed columns"""
        try:
            csv_filename = f"{config['primary_pt']}_{config['accessory_pt']}_{config['acc_pri']}_{config['mp']}_claude_results.csv"
            csv_path = os.path.join(task_dir, csv_filename)
            
            parsed_results = []
            
            # Read and parse log file
            with open(log_file_path, 'r', encoding='utf-8') as log_file:
                for line_num, line in enumerate(log_file, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        # Parse JSON line
                        log_entry = json.loads(line)
                        asin = log_entry.get('asin', f'unknown_{line_num}')
                        claude_response = log_entry.get('claude_response', 'no output')
                        
                        # Parse Claude's response
                        parsed_response = self.parse_claude_response(claude_response)
                        
                        # Create structured row
                        parsed_results.append({
                            'asin': asin,
                            'claude_response_raw': claude_response,
                            'in_scope': parsed_response['in_scope'],
                            'reason': parsed_response['reason'],
                            'pt_pair': config['pt_pair'],
                            'primary_pt': config['primary_pt'],
                            'accessory_pt': config['accessory_pt'],
                            'acc_pri': config['acc_pri'],
                            'marketplace': 'us',
                            'processed_at': datetime.now().isoformat()
                        })
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse line {line_num} in log file: {e}")
                        # Add error row
                        parsed_results.append({
                            'asin': f'parse_error_{line_num}',
                            'claude_response_raw': line[:100] + '...' if len(line) > 100 else line,
                            'in_scope': 'parse_error',
                            'reason': f'JSON parse error: {str(e)}',
                            'pt_pair': config['pt_pair'],
                            'primary_pt': config['primary_pt'],
                            'accessory_pt': config['accessory_pt'],
                            'acc_pri': config['acc_pri'],
                            'marketplace': 'us',
                            'processed_at': datetime.now().isoformat()
                        })
                    except Exception as e:
                        logger.warning(f"Unexpected error parsing line {line_num}: {e}")
            
            if not parsed_results:
                logger.warning(f"No valid results found in log file: {log_file_path}")
                return None
            
            # Convert to DataFrame and save as CSV
            results_df = pd.DataFrame(parsed_results)
            results_df.to_csv(csv_path, index=False, encoding='utf-8')
            
            return csv_path
            
        except Exception as e:
            logger.error(f"Error converting log to CSV: {str(e)}")
            return None
    
    def save_file_results_to_csv(self, results: List[Dict[str, Any]], task_dir: str, 
                                primary_pt: str, accessory_pt: str, acc_pri: str, mp: str = "us") -> str:
        """Save individual file results to CSV (legacy method - kept for compatibility)"""
        # Process results to add in_scope extraction
        processed_results = []
        for result in results:
            processed_result = result.copy()
            processed_result["in_scope"] = self.in_scope_extract(result["claude_response"])
            processed_result["pt_pair"] = f"{primary_pt}_{accessory_pt}"
            processed_result["primary_pt"] = primary_pt
            processed_result["accessory_pt"] = accessory_pt
            processed_result["acc_pri"] = acc_pri
            processed_result["marketplace"] = mp
            processed_result["processed_at"] = datetime.now().isoformat()
            processed_results.append(processed_result)
        
        # Generate CSV filename
        csv_filename = f"{primary_pt}_{accessory_pt}_{acc_pri}_{mp}_in_scope_results.csv"
        csv_path = os.path.join(task_dir, csv_filename)
        
        # Save to CSV
        results_df = pd.DataFrame(processed_results)
        results_df.to_csv(csv_path, index=False, encoding='utf-8')
        
        logger.info(f"Saved {len(processed_results)} results to CSV: {csv_path}")
        return csv_path
    
    def upload_file_to_s3(self, local_file_path: str, task_id: str) -> str:
        """Upload file to S3 and return the S3 URL"""
        filename = os.path.basename(local_file_path)
        s3_key = f"ml_output/{task_id}/{filename}"
        
        try:
            # Determine content type based on file extension
            content_type = 'text/csv' if filename.endswith('.csv') else 'text/plain'
            
            with open(local_file_path, 'rb') as file:
                self.s3_client.put_object(
                    Bucket=AWS_S3_BUCKET,
                    Key=s3_key,
                    Body=file.read(),
                    ContentType=content_type,
                    Metadata={
                        'task_id': task_id,
                        'upload_type': 'claude_results' if 'claude_results' in filename else 'raw_log',
                        'uploaded_at': datetime.now().isoformat()
                    }
                )
            
            s3_url = f"s3://{AWS_S3_BUCKET}/{s3_key}"
            logger.info(f"Uploaded {filename} to S3: {s3_url}")
            return s3_url
            
        except Exception as e:
            logger.error(f"Error uploading {filename} to S3: {str(e)}")
            raise
    
    def update_task_with_file_results(self, task_id: str, file_results: List[str]) -> bool:
        """Update task with file result S3 URLs"""
        try:
            # Get current task to preserve existing data
            current_task = self.get_task(task_id)
            if not current_task:
                logger.error(f"Task {task_id} not found for file results update")
                return False
            
            # Add or update file_results field
            existing_file_results = current_task.get('file_results', [])
            all_file_results = existing_file_results + file_results
            
            self.task_table.update_item(
                Key={'task_id': task_id},
                UpdateExpression="SET file_results = :file_results, updated_at = :timestamp",
                ExpressionAttributeValues={
                    ':file_results': all_file_results,
                    ':timestamp': datetime.now().isoformat()
                }
            )
            
            logger.info(f"Updated task {task_id} with {len(file_results)} new file results")
            return True
            
        except Exception as e:
            logger.error(f"Error updating task {task_id} with file results: {str(e)}")
            return False
    
    def save_results(self, results: List[Dict[str, Any]], task_id: str, 
                    gv_ceiling: float, pt_pair: str) -> str:
        """Save results to S3"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Process results to add in_scope extraction
        processed_results = []
        for result in results:
            processed_result = result.copy()
            processed_result["in_scope"] = self.in_scope_extract(result["claude_response"])
            processed_results.append(processed_result)
        
        # Save as JSON
        json_key = f"results/{pt_pair}_{timestamp}_{task_id}_results.json"
        json_content = {
            "task_id": task_id,
            "pt_pair": pt_pair,
            "timestamp": timestamp,
            "results": processed_results,
            "gv_ceiling": gv_ceiling,
            "summary": {
                "total_items": len(processed_results),
                "gv_ceiling": gv_ceiling,
                "in_scope_count": sum(1 for r in processed_results if r.get("in_scope") == "yes"),
                "out_of_scope_count": sum(1 for r in processed_results if r.get("in_scope") == "no"),
                "unknown_count": sum(1 for r in processed_results if r.get("in_scope") == "unknown")
            }
        }
        
        self.s3_client.put_object(
            Bucket=AWS_S3_BUCKET,
            Key=json_key,
            Body=json.dumps(json_content, indent=2),
            ContentType='application/json'
        )
        
        # Save as Excel
        excel_key = f"results/{pt_pair}_{timestamp}_{task_id}_results.xlsx"
        results_df = pd.DataFrame(processed_results)
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            results_df.to_excel(tmp_file.name, index=False)
            
            with open(tmp_file.name, 'rb') as f:
                self.s3_client.put_object(
                    Bucket=AWS_S3_BUCKET,
                    Key=excel_key,
                    Body=f.read(),
                    ContentType='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
            
            os.unlink(tmp_file.name)
        
        logger.info(f"Results saved to S3: {excel_key}")
        return f"s3://{AWS_S3_BUCKET}/{excel_key}"
    
    def process_task(self, task_id: str) -> bool:
        """Process a single task with improved file handling and parallel processing"""
        try:
            logger.info(f"Starting processing for task {task_id}")
            
            # Update status to processing
            self.update_task_status(task_id, "processing", progress=0)
            
            # Get task details
            task = self.get_task(task_id)
            if not task:
                raise Exception(f"Task {task_id} not found")
            
            # Step 1: Download and extract files
            logger.info(f"Downloading and extracting files from {task['s3_uri']}")
            task_dir = self.download_and_extract_files(task['s3_uri'], task_id)
            
            # Step 2: Find all data files
            data_files = self.find_data_files(task_dir)
            if not data_files:
                raise Exception("No valid data files found in the uploaded content")
            
            self.update_task_status(task_id, "processing", progress=5)
            
            # Get compatibility criterion and basic config
            pt_pair_upper = task['pt_pair'].upper()
            compatibility_criterion = get_comp_criterion(pt_pair_upper)
            primary_pt = task['pt_pair'].split("-")[0].lower()
            accessory_pt = task['pt_pair'].split("-")[1].lower()
            task_model = task.get('model', DEFAULT_MODEL)
            
            all_results = []
            total_files = len(data_files)
            
            # Process each data file
            for file_idx, file_info in enumerate(data_files):
                logger.info(f"Processing file {file_idx + 1}/{total_files}: {file_info['filename']}")
                
                # Read data file
                data_df = self.read_data_file(file_info['file_path'])
                if len(data_df) == 0:
                    logger.warning(f"No data found in file {file_info['filename']}, skipping")
                    continue
                
                # Set up configuration for this file
                acc_pri = file_info['acc_pri']
                current_pt = primary_pt if acc_pri == 'primary' else accessory_pt
                
                config = {
                    "pt_pair": task['pt_pair'].lower(),
                    "primary_pt": primary_pt,
                    "accessory_pt": accessory_pt,
                    "current_pt": current_pt,
                    "acc_pri": acc_pri,
                    "compatibility_criterion": compatibility_criterion,
                    "mp": file_info['mp'] # Default marketplace
                }
                
                logger.info(f"Processing {len(data_df)} items with acc_pri='{acc_pri}', current_pt='{current_pt}'")
                
                # Step 3: Process images and LLM in parallel with 30-second offset
                image_thread = None
                image_completed = threading.Event()
                
                def run_image_processing():
                    try:
                        if task_model != "Claude 3.5 Haiku":
                            self.process_images(data_df, config, task_id)
                    except Exception as e:
                        logger.error(f"Error in image processing: {str(e)}")
                    finally:
                        image_completed.set()
                
                # Start image processing first
                if task_model != "Claude 3.5 Haiku":
                    image_thread = threading.Thread(target=run_image_processing)
                    image_thread.start()
                    logger.info("Started image processing in background")
                
                # Wait 30 seconds or until image processing is complete
                logger.info("Waiting 30 seconds before starting LLM processing...")
                if image_thread:
                    image_completed.wait(timeout=30)
                    if image_completed.is_set():
                        logger.info("Image processing completed before timeout")
                    else:
                        logger.info("Starting LLM processing after 30-second timeout")
                else:
                    time.sleep(30)  # Still wait if no image processing needed
                
                # Step 4: Process with LLM using parallel execution
                file_results = self.process_with_llm_parallel(data_df, config, task_id, task_dir, task_model)
                all_results.extend(file_results)
                
                # Wait for image processing to complete if still running
                if image_thread and image_thread.is_alive():
                    logger.info("Waiting for image processing to complete...")
                    image_thread.join()
                
                # Step 5: Convert log to structured CSV
                log_file_path = os.path.join(task_dir, f"{config['current_pt']}.log")
                parsed_csv_path = None
                
                if os.path.exists(log_file_path):
                    # Convert log file to structured CSV
                    parsed_csv_path = self.convert_log_to_csv(log_file_path, task_dir, config)
                    
                    if parsed_csv_path:
                        logger.info(f"Successfully converted log to CSV: {parsed_csv_path}")
                    else:
                        logger.warning(f"Failed to convert log to CSV, falling back to legacy method")
                        # Fallback to legacy CSV generation
                        parsed_csv_path = self.save_file_results_to_csv(
                            file_results, task_dir, primary_pt, accessory_pt, acc_pri, "us"
                        )
                else:
                    logger.warning(f"Log file not found: {log_file_path}, using legacy CSV generation")
                    # Fallback to legacy CSV generation
                    parsed_csv_path = self.save_file_results_to_csv(
                        file_results, task_dir, primary_pt, accessory_pt, acc_pri, "us"
                    )
                
                # Step 6: Upload parsed CSV to S3
                file_s3_urls = []
                
                if parsed_csv_path and os.path.exists(parsed_csv_path):
                    # Upload the parsed CSV file (this is the main result file)
                    csv_s3_url = self.upload_file_to_s3(parsed_csv_path, task_id)
                    file_s3_urls.append(csv_s3_url)
                    logger.info(f"Uploaded parsed CSV to S3: {csv_s3_url}")
                else:
                    logger.error(f"Parsed CSV file not found or creation failed: {parsed_csv_path}")
                
                # Optionally upload raw log file for debugging (comment out if not needed)
                if os.path.exists(log_file_path):
                    try:
                        log_s3_url = self.upload_file_to_s3(log_file_path, task_id)
                        file_s3_urls.append(log_s3_url)
                        logger.info(f"Uploaded raw log to S3: {log_s3_url}")
                    except Exception as e:
                        logger.warning(f"Failed to upload raw log file: {str(e)}")
                
                # Step 7: Update DynamoDB with file result URLs
                self.update_task_with_file_results(task_id, file_s3_urls)
                
                logger.info(f"Completed processing file {file_info['filename']}. Uploaded {len(file_s3_urls)} files to S3.")
                
                # Update progress based on file completion
                file_progress = 10 + int((file_idx + 1) / total_files * 70)  # 10-80% range
                self.update_task_status(task_id, "processing", progress=file_progress)
            
            if not all_results:
                raise Exception("No results generated from any data files")
            
            self.update_task_status(task_id, "processing", progress=85)
            
            # Step 5: Calculate GV ceiling using all results
            logger.info("Calculating GV ceiling for all results")
            
            # # Combine all data for GV ceiling calculation
            # all_data_dfs = []
            # for file_info in data_files:
            #     file_df = self.read_data_file(file_info['file_path'])
            #     if len(file_df) > 0:
            #         all_data_dfs.append(file_df)
            
            # if all_data_dfs:
            #     combined_data_df = pd.concat(all_data_dfs, ignore_index=True)
            #     results_df = pd.DataFrame(all_results)
            #     results_df["in_scope"] = results_df["claude_response"].apply(self.in_scope_extract)
            #     gv_ceiling = calculate_gv_ceiling(combined_data_df, results_df)
            # else:
            #     gv_ceiling = 0.0
            
            self.update_task_status(task_id, "processing", progress=90)
            
            # # Step 6: Save results
            # result_file_url = self.save_results(all_results, task_id, gv_ceiling, task['pt_pair'])
            
            # Step 7: Clean up task directory
            try:
                import shutil
                shutil.rmtree(task_dir)
                logger.info(f"Cleaned up task directory: {task_dir}")
            except Exception as e:
                logger.warning(f"Failed to clean up task directory: {str(e)}")
            
            # Update task as completed
            self.update_task_status(
                task_id, 
                "completed",
                progress=100,
                processed_items=len(all_results),
                result_file_url=f"s3://{AWS_S3_BUCKET}/ml_output/{task_id}/",
            )
            
            # logger.info(f"Task {task_id} completed successfully. Processed {len(all_results)} items across {total_files} files. GV Ceiling: {gv_ceiling}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing task {task_id}: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Clean up task directory on error
            try:
                task_dir = os.path.join('./data', task_id)
                if os.path.exists(task_dir):
                    import shutil
                    shutil.rmtree(task_dir)
            except Exception:
                pass
            
            self.update_task_status(
                task_id, 
                "failed", 
                error_message=str(e)
            )
            return False