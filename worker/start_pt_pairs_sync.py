#!/usr/bin/env python3
"""
Startup script for PT pairs synchronization service

This script sets up and starts the PT pairs sync service that keeps DynamoDB
in sync with the build_prompt.py source code.
"""

import sys
import os
import logging
import subprocess
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pt_pairs_sync import PTairsSyncService

def setup_logging():
    """Configure logging for the sync service"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler('pt_pairs_sync.log'),
            logging.StreamHandler()
        ]
    )

def check_prerequisites():
    """Check if all prerequisites are met"""
    logger = logging.getLogger(__name__)
    
    # Check if setup script exists
    setup_script = os.path.join(os.path.dirname(__file__), 'setup_pt_pairs_table.py')
    if not os.path.exists(setup_script):
        logger.error(f"Setup script not found: {setup_script}")
        return False
    
    # Check if sync service script exists
    sync_script = os.path.join(os.path.dirname(__file__), 'pt_pairs_sync.py')
    if not os.path.exists(sync_script):
        logger.error(f"Sync service script not found: {sync_script}")
        return False
    
    return True

def setup_dynamodb_table():
    """Set up the DynamoDB table"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Setting up DynamoDB table...")
        setup_script = os.path.join(os.path.dirname(__file__), 'setup_pt_pairs_table.py')
        
        result = subprocess.run(
            [sys.executable, setup_script], 
            capture_output=True, 
            text=True, 
            cwd=os.path.dirname(__file__)
        )
        if result.returncode == 0:
            logger.info("DynamoDB table setup completed successfully")
            logger.info(result.stdout)
            return True
        else:
            logger.error(f"DynamoDB table setup failed: {result.stderr}")         
            return False
    
    except Exception as e:
        logger.error(f"Error setting up DynamoDB table: {e}")
        return False

def run_initial_sync():
    """Run initial synchronization"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Running initial synchronization...")
        sync_service = PTairsSyncService()
        
        # Verify table exists
        if not sync_service.verify_table_exists():
            logger.error("DynamoDB table verification failed")
            return False
        
        # Run initial sync
        results = sync_service.sync_all_pt_pairs()
        
        if results['success']:
            logger.info(f"Initial sync completed: {results['updated_pairs']}/{results['total_pairs']} PT pairs updated")
            if results['errors']:
                logger.warning(f"Initial sync completed with {len(results['errors'])} errors")
                for error in results['errors']:
                    logger.warning(f"  - {error}")
            return True
        else:
            logger.error(f"Initial sync failed: {results.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"Error during initial sync: {e}")
        return False

def main():
    """Main function"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=== PT Pairs Sync Service Startup ===")
    logger.info(f"Started at: {datetime.now().isoformat()}")
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("Prerequisites check failed. Exiting.")
        sys.exit(1)
    
    # Setup DynamoDB table
    if not setup_dynamodb_table():
        logger.error("DynamoDB table setup failed. Exiting.")
        sys.exit(1)
    
    # Run initial sync
    if not run_initial_sync():
        logger.error("Initial synchronization failed. Exiting.")
        sys.exit(1)
    
    # Start continuous sync service
    logger.info("Starting continuous sync service...")
    try:
        sync_service = PTairsSyncService()
        sync_service.run_sync_loop(30)  # 30 seconds interval
    except KeyboardInterrupt:
        logger.info("Sync service stopped by user")
    except Exception as e:
        logger.error(f"Sync service error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()