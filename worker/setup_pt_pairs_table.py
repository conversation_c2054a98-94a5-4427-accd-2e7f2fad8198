#!/usr/bin/env python3
"""
Setup script for PT pairs and prompts DynamoDB table
"""

import boto3
import sys
import os
from botocore.exceptions import ClientError

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), "src"))

from config import AWS_REGION

def create_pt_pairs_table():
    """Create DynamoDB table for PT pairs and prompts"""
    
    dynamodb = boto3.resource('dynamodb', region_name=AWS_REGION)
    
    table_name = 'auto-in-scope-pt-pairs'
    
    try:
        # Check if table already exists
        existing_table = dynamodb.Table(table_name)
        existing_table.table_status
        print(f"Table {table_name} already exists")
        return existing_table
        
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceNotFoundException':
            # Table doesn't exist, create it
            print(f"Creating table {table_name}...")
            
            table = dynamodb.create_table(
                TableName=table_name,
                KeySchema=[
                    {
                        'AttributeName': 'pt_pair',
                        'KeyType': 'HASH'  # Partition key
                    }
                ],
                AttributeDefinitions=[
                    {
                        'AttributeName': 'pt_pair',
                        'AttributeType': 'S'
                    }
                ],
                BillingMode='PAY_PER_REQUEST'  # On-demand billing
            )
            
            # Wait for table to be created
            print("Waiting for table to be created...")
            table.wait_until_exists()
            
            print(f"Table {table_name} created successfully!")
            return table
        else:
            print(f"Error checking table: {e}")
            raise

if __name__ == "__main__":
    try:
        table = create_pt_pairs_table()
        print("PT pairs table setup completed successfully!")
    except Exception as e:
        print(f"Error setting up PT pairs table: {e}")
        sys.exit(1)