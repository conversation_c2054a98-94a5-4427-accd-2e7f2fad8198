import boto3
import time
import logging
import concurrent.futures
from typing import List, Dict, Any
from datetime import datetime
import signal
import sys
from task_processor import TaskProcessor
from config import *

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DynamoDBListener:
    def __init__(self):
        self.dynamodb = boto3.resource('dynamodb', region_name=AWS_REGION)
        self.task_table = self.dynamodb.Table(AWS_TASK_TABLE)
        self.task_processor = TaskProcessor()
        self.running = True
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_TASKS)
        self.active_tasks = set()
        # Track active tasks by model to ensure one task per model
        self.active_models = set()
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}. Initiating graceful shutdown...")
        self.running = False
        
        # Wait for active tasks to complete
        if self.active_tasks:
            logger.info(f"Waiting for {len(self.active_tasks)} active tasks to complete...")
            while self.active_tasks:
                time.sleep(5)
                logger.info(f"Still waiting for {len(self.active_tasks)} tasks...")
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        logger.info("Graceful shutdown completed")
        sys.exit(0)
    
    def get_waiting_tasks(self) -> List[Dict[str, Any]]:
        """Get tasks with status 'waiting' from DynamoDB"""
        try:
            response = self.task_table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('status').eq('waiting')
            )
            
            tasks = response['Items']
            
            # Sort by creation time (oldest first)
            tasks.sort(key=lambda x: x.get('created_at', ''))
            
            return tasks
            
        except Exception as e:
            logger.error(f"Error scanning for waiting tasks: {str(e)}")
            return []
    
    def process_task_wrapper(self, task_id: str, model: str):
        """Wrapper for processing task with proper cleanup"""
        try:
            logger.info(f"Starting task processing: {task_id} with model: {model}")
            self.active_tasks.add(task_id)
            self.active_models.add(model)
            success = self.task_processor.process_task(task_id)
            if success:
                logger.info(f"Task {task_id} completed successfully")
            else:
                logger.error(f"Task {task_id} failed")
        except Exception as e:
            logger.error(f"Unexpected error processing task {task_id}: {str(e)}")
        finally:
            self.active_tasks.discard(task_id)
            self.active_models.discard(model)
    
    def can_process_more_tasks(self) -> bool:
        """Check if we can process more tasks based on current load"""
        return len(self.active_tasks) < MAX_CONCURRENT_TASKS
    
    def can_process_model(self, model: str) -> bool:
        """Check if the specified model is available (not currently in use)"""
        return model not in self.active_models
    
    def start_listening(self):
        """Start the main listening loop"""
        logger.info(f"Starting DynamoDB listener for table: {AWS_TASK_TABLE}")
        logger.info(f"Max concurrent tasks: {MAX_CONCURRENT_TASKS}")
        logger.info(f"Poll interval: {POLL_INTERVAL} seconds")
        
        while self.running:
            try:
                # Check for waiting tasks
                waiting_tasks = self.get_waiting_tasks()
                
                if waiting_tasks:
                    logger.info(f"Found {len(waiting_tasks)} waiting tasks")
                    
                    # Process tasks up to our concurrency limit and model availability
                    for task in waiting_tasks:
                        if not self.running:
                            break
                            
                        if not self.can_process_more_tasks():
                            logger.info("Max concurrent tasks reached, skipping remaining tasks")
                            break
                        
                        task_id = task['task_id']
                        task_model = task.get('model', 'Claude 3.5 Sonnet V2')
                        
                        # Skip if already processing
                        if task_id in self.active_tasks:
                            continue
                        
                        # Check if the model is available (not currently being used)
                        if not self.can_process_model(task_model):
                            logger.info(f"Model {task_model} is busy, skipping task {task_id}")
                            continue
                        
                        # Submit task for processing
                        logger.info(f"Submitting task {task_id} for processing with model {task_model}")
                        self.executor.submit(self.process_task_wrapper, task_id, task_model)
                
                else:
                    logger.debug("No waiting tasks found")
                
                # Show current status
                if self.active_tasks:
                    logger.info(f"Currently processing {len(self.active_tasks)} tasks: {list(self.active_tasks)}")
                    logger.info(f"Active models: {list(self.active_models)}")
                
                # Wait before next poll
                time.sleep(POLL_INTERVAL)
                
            except KeyboardInterrupt:
                logger.info("Received keyboard interrupt")
                break
            except Exception as e:
                logger.error(f"Error in main listening loop: {str(e)}")
                time.sleep(POLL_INTERVAL * 2)  # Wait longer on errors
        
        logger.info("Listener stopped")


def main():
    """Main entry point"""
    listener = DynamoDBListener()
    
    try:
        listener.start_listening()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt in main")
    except Exception as e:
        logger.error(f"Fatal error in main: {str(e)}")
    finally:
        # Ensure proper cleanup
        listener.running = False
        if hasattr(listener, 'executor'):
            listener.executor.shutdown(wait=True)


if __name__ == "__main__":
    main()