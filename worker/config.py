import os

# AWS Configuration
# AWS_PROFILE = "compatibility"  # Comment out to use default credentials
AWS_REGION = "us-east-1"
AWS_S3_BUCKET = "auto-in-scope"  # Updated to match backend config
AWS_TASK_TABLE = "auto-in-scope-tasks"
AWS_CLAUDE_ACCOUNT_TABLE = "claude_account"

# Task Processing Configuration
MAX_CONCURRENT_TASKS = 3
DEFAULT_MODEL = "Claude 3.5 Sonnet V2"
POLL_INTERVAL = 10  # seconds

# Marketplace Configuration
MARKETPLACES = ["us", "uk", "de", "fr", "ca", "it", "es"]

# File paths
DATA_DIR = os.path.join(os.path.dirname(__file__), "data")
os.makedirs(DATA_DIR, exist_ok=True)