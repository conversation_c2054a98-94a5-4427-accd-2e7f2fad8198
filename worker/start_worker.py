#!/usr/bin/env python3
"""
Worker startup script for Auto In Scope analysis system
"""
import os
import sys
import logging
import argparse
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from listener import DynamoDBListener
from config import AWS_TASK_TABLE, MAX_CONCURRENT_TASKS, POLL_INTERVAL

def setup_logging(log_level="INFO"):
    """Setup logging configuration"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f"worker_{datetime.now().strftime('%Y%m%d')}.log")
        ]
    )
    
    # Reduce boto3 logging
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def print_startup_banner():
    """Print startup information"""
    print("=" * 60)
    print("  Auto In Scope Worker Service")
    print("=" * 60)
    print(f"  DynamoDB Table: {AWS_TASK_TABLE}")
    print(f"  Max Concurrent Tasks: {MAX_CONCURRENT_TASKS}")
    print(f"  Poll Interval: {POLL_INTERVAL} seconds")
    print(f"  Started at: {datetime.now().isoformat()}")
    print("=" * 60)
    print()


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Auto In Scope Worker Service")
    parser.add_argument(
        "--log-level", 
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Set logging level"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in dry-run mode (don't process tasks)"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # Print startup banner
    print_startup_banner()
    
    if args.dry_run:
        logger.warning("Running in DRY-RUN mode - tasks will not be processed")
    
    try:
        # Create and start listener
        listener = DynamoDBListener()
        
        if args.dry_run:
            logger.info("Dry run completed - would start listening now")
        else:
            listener.start_listening()
            
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}", exc_info=True)
        sys.exit(1)
    
    logger.info("Worker service shutdown complete")


if __name__ == "__main__":
    main()