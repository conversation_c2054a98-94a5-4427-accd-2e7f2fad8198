# Worker CLAUDE.md

## Overview

The worker component is a distributed task processing system that handles the actual compatibility analysis work. It monitors DynamoDB for pending tasks, processes them using LLM (Large Language Model) analysis, and saves results back to S3. This is the core processing engine of the Auto In Scope system.

## Key Architecture

### Technology Stack

- **Python 3** - Core runtime environment
- **AWS SDK (Boto3)** - AWS service integration
- **DynamoDB** - Task queue and status management
- **S3** - Input/output file storage
- **AWS Bedrock** - LLM inference via Claude models
- **Pandas** - Data processing and analysis
- **Threading** - Concurrent task processing

### Core Components

- **listener.py**: DynamoDB task listener and orchestrator
- **task_processor.py**: Individual task processing logic
- **main.py**: Legacy standalone processing (batch mode)
- **start_worker.py**: Worker service startup script
- **config.py**: Configuration and settings management

### Processing Flow

1. **Task Discovery**: Listener scans DynamoDB for tasks with "waiting" status
2. **Task Processing**: Worker downloads data, processes with LLM, generates results
3. **Result Storage**: Saves analysis results to S3 with progress tracking
4. **Status Updates**: Updates task status in DynamoDB throughout processing

### DynamoDB Listener (`listener.py`)

#### Key Features
- **Polling System**: Continuously scans for waiting tasks
- **Concurrency Control**: Manages multiple tasks simultaneously (configurable limit)
- **Graceful Shutdown**: Handles termination signals properly
- **Error Recovery**: Resilient to temporary failures

#### Task Management
- Monitors tasks with `status = "waiting"`
- Submits tasks to thread pool for parallel processing
- Tracks active tasks to prevent overload
- Updates task status as processing progresses

### Task Processor (`task_processor.py`)

#### Processing Pipeline
1. **Data Acquisition**
   - Downloads input file from S3
   - Supports Excel, CSV, TSV formats
   - Validates data structure and content

2. **Data Preprocessing**
   - Applies stratified sampling for large datasets (>8000 items)
   - Extracts product ASINs and metadata
   - Prepares data for LLM analysis

3. **Image Processing**
   - Collects product images from Amazon marketplaces
   - Uploads images to S3 for LLM analysis
   - Parallel processing for efficiency

4. **LLM Analysis**
   - Uses Claude models via AWS Bedrock
   - Applies product-specific compatibility criteria
   - Processes items in batches with progress tracking

5. **Result Generation**
   - Extracts compatibility decisions from LLM responses
   - Calculates GV (Gross Value) ceiling metrics
   - Generates structured output files

6. **Storage & Completion**
   - Saves results as JSON and Excel files to S3
   - Updates task status to "completed"
   - Provides result file URLs

### Configuration (`config.py`)

Key settings include:
- `MAX_CONCURRENT_TASKS`: Maximum parallel tasks (default: 3)
- `POLL_INTERVAL`: Task polling frequency (default: 30 seconds)
- `AWS_REGION`: AWS region for services
- `AWS_TASK_TABLE`: DynamoDB table name
- `AWS_S3_BUCKET`: S3 bucket for file storage
- `DEFAULT_MODEL`: LLM model to use (Claude 3.5 Sonnet V2)

### LLM Integration

#### Models Used
- **Primary**: Claude 3.5 Sonnet V2
- **Fallback**: Other Claude models as configured

#### Analysis Process
- Builds prompts with product data and compatibility criteria
- Includes product images when available
- Processes responses to extract compatibility decisions
- Handles API failures with retry logic

### Error Handling

#### Task Status Management
- **waiting**: Task submitted, not yet processed
- **processing**: Currently being processed by worker
- **completed**: Successfully finished
- **failed**: Encountered unrecoverable error

#### Recovery Mechanisms
- Automatic retry for transient failures
- Graceful degradation for missing images
- Comprehensive error logging
- Status updates for failed tasks

### Monitoring & Logging

#### Progress Tracking
- Real-time progress percentages (0-100%)
- Item-level processing counts
- Status updates throughout pipeline

#### Logging Features
- Structured logging with timestamps
- Error tracking with stack traces
- Performance metrics and timing
- Task completion statistics

## Common Commands

```bash
# Start worker listener
python listener.py

# Start worker with startup script
python start_worker.py

# Run standalone processing (legacy)
python main.py

# Install dependencies
pip install -r requirements.txt
```

## Development Notes

- **Shared Source Code**: Uses same `src/` modules as root project
- **Concurrent Processing**: Thread pool for handling multiple tasks
- **Resource Management**: Controlled concurrency to prevent overload
- **AWS Integration**: Deep integration with DynamoDB, S3, and Bedrock
- **Scalability**: Can run multiple worker instances for higher throughput
- **Monitoring**: Built-in progress tracking and status reporting

### Performance Characteristics

- **Image Processing**: Up to 20 concurrent image uploads
- **LLM Processing**: Batch processing with progress updates
- **Memory Usage**: Optimized for large dataset processing
- **Fault Tolerance**: Handles network issues and service interruptions

### Deployment Considerations

- Can run as standalone service or in containerized environment
- Requires AWS credentials with appropriate permissions
- Scalable horizontally by running multiple instances
- Monitoring and alerting should track task completion rates