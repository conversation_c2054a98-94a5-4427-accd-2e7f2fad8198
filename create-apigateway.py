#!/usr/bin/env python3
"""
Create API Gateway for Elastic Beanstalk backend
"""

import boto3
import json
from botocore.exceptions import ClientError

def create_api_gateway():
    """Create API Gateway for the Elastic Beanstalk backend"""
    apigateway = boto3.client('apigateway')
    
    backend_url = 'awseb--AWSEB-SLehZRb250sx-1889801112.us-east-1.elb.amazonaws.com'
    api_name = 'auto-in-scope-api'
    
    try:
        # Check if API exists
        response = apigateway.get_rest_apis()
        for api in response['items']:
            if api['name'] == api_name:
                api_id = api['id']
                print(f"✅ API Gateway {api_name} already exists: {api_id}")
                
                # Get existing endpoint
                deployments = apigateway.get_deployments(restApiId=api_id)
                if deployments['items']:
                    api_url = f"https://{api_id}.execute-api.us-east-1.amazonaws.com/prod"
                    print(f"✅ API Gateway URL: {api_url}")
                    return api_url
        
    except ClientError:
        pass
    
    try:
        # Create REST API
        print("🚀 Creating API Gateway...")
        response = apigateway.create_rest_api(
            name=api_name,
            description='Auto In Scope Backend API Gateway',
            endpointConfiguration={
                'types': ['REGIONAL']
            }
        )
        
        api_id = response['id']
        print(f"✅ Created API Gateway: {api_id}")
        
        # Get root resource ID
        resources = apigateway.get_resources(restApiId=api_id)
        root_id = None
        for resource in resources['items']:
            if resource['path'] == '/':
                root_id = resource['id']
                break
        
        # Create {proxy+} resource for catch-all
        proxy_resource = apigateway.create_resource(
            restApiId=api_id,
            parentId=root_id,
            pathPart='{proxy+}'
        )
        proxy_resource_id = proxy_resource['id']
        print(f"✅ Created proxy resource: {proxy_resource_id}")
        
        # Create ANY method for proxy resource
        apigateway.put_method(
            restApiId=api_id,
            resourceId=proxy_resource_id,
            httpMethod='ANY',
            authorizationType='NONE',
            requestParameters={
                'method.request.path.proxy': True
            }
        )
        print("✅ Created ANY method for proxy")
        
        # Set up integration
        integration_uri = f"http://{backend_url}/{{proxy}}"
        apigateway.put_integration(
            restApiId=api_id,
            resourceId=proxy_resource_id,
            httpMethod='ANY',
            type='HTTP_PROXY',
            integrationHttpMethod='ANY',
            uri=integration_uri,
            requestParameters={
                'integration.request.path.proxy': 'method.request.path.proxy'
            }
        )
        print(f"✅ Created integration to: {integration_uri}")
        
        # Create ANY method for root resource
        apigateway.put_method(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='ANY',
            authorizationType='NONE'
        )
        
        # Integration for root
        apigateway.put_integration(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='ANY',
            type='HTTP_PROXY',
            integrationHttpMethod='ANY',
            uri=f"http://{backend_url}"
        )
        print("✅ Created root integration")
        
        # Create OPTIONS method for CORS (root resource)
        apigateway.put_method(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='OPTIONS',
            authorizationType='NONE'
        )
        
        # CORS integration for root
        apigateway.put_integration(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='OPTIONS',
            type='MOCK',
            requestTemplates={
                'application/json': '{"statusCode": 200}'
            }
        )
        
        # Method response for OPTIONS
        apigateway.put_method_response(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='OPTIONS',
            statusCode='200',
            responseParameters={
                'method.response.header.Access-Control-Allow-Headers': False,
                'method.response.header.Access-Control-Allow-Methods': False,
                'method.response.header.Access-Control-Allow-Origin': False
            }
        )
        
        # Integration response for OPTIONS  
        apigateway.put_integration_response(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='OPTIONS',
            statusCode='200',
            responseParameters={
                'method.response.header.Access-Control-Allow-Headers': "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
                'method.response.header.Access-Control-Allow-Methods': "'GET,POST,PUT,DELETE,OPTIONS'",
                'method.response.header.Access-Control-Allow-Origin': "'*'"
            }
        )
        print("✅ Created CORS configuration")
        
        # Deploy API
        deployment = apigateway.create_deployment(
            restApiId=api_id,
            stageName='prod',
            description='Production deployment'
        )
        
        api_url = f"https://{api_id}.execute-api.us-east-1.amazonaws.com/prod"
        print(f"✅ API Gateway deployed: {api_url}")
        
        return api_url
        
    except ClientError as e:
        print(f"❌ Error creating API Gateway: {e}")
        return None

def test_api_gateway(api_url):
    """Test the API Gateway endpoints"""
    print(f"🧪 Testing API Gateway...")
    
    import subprocess
    import time
    
    time.sleep(5)  # Wait for deployment
    
    try:
        # Test health endpoint
        result = subprocess.run(['curl', '-s', f'{api_url}/health'], 
                              capture_output=True, text=True, timeout=15)
        if result.returncode == 0 and 'healthy' in result.stdout:
            print(f"✅ API Gateway health check passed!")
        else:
            print(f"⚠️ Health check result: {result.stdout}")
            
        # Test API endpoint
        result2 = subprocess.run(['curl', '-s', f'{api_url}/api/pt-pairs'], 
                               capture_output=True, text=True, timeout=15)
        if result2.returncode == 0 and '[' in result2.stdout:
            print(f"✅ API Gateway API test passed!")
        else:
            print(f"⚠️ API test result: {result2.stdout}")
            
    except Exception as e:
        print(f"⚠️ Test failed: {e}")

def main():
    """Main function"""
    print("🚀 Creating API Gateway for Elastic Beanstalk backend...")
    
    api_url = create_api_gateway()
    if not api_url:
        return
    
    test_api_gateway(api_url)
    
    # Summary
    print("\n" + "="*60)
    print("🎉 API Gateway Setup Complete!")
    print("="*60)
    print(f"API Gateway URL: {api_url}")
    print(f"Health Check: {api_url}/health")
    print(f"PT Pairs API: {api_url}/api/pt-pairs")
    print(f"Tasks API: {api_url}/api/tasks")
    print("\n📝 Next steps:")
    print(f"1. Update frontend REACT_APP_API_URL to: {api_url}/api")
    print(f"2. Deploy frontend to Amplify")
    print(f"3. Test full application flow")

if __name__ == "__main__":
    main()