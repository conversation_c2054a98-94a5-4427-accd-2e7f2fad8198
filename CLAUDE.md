# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The Auto In Scope system is a modern, distributed product compatibility analysis platform that uses advanced AI (Claude models) to determine whether products are compatible with each other based on predefined criteria. The system processes product data from Amazon marketplaces and generates comprehensive compatibility assessments through a sophisticated three-tier architecture.

## System Architecture

This is a complete web application with three distinct layers:

### 1. Frontend (React TypeScript)
- **Location**: `frontend/`
- **Technology**: React 19 with TypeScript, Ant Design UI, AWS integration
- **Purpose**: Modern web interface for task submission and result viewing
- **Key Features**:
  - Product type pair selection (26 predefined compatibility pairs)
  - Secure file upload to S3 with progress tracking
  - Real-time task status monitoring
  - Result download and management
  - Responsive design with Linear-inspired aesthetics

### 2. Backend API (FastAPI)
- **Location**: `backend/`
- **Technology**: FastAPI with Pydantic, AWS SDK (boto3), DynamoDB
- **Purpose**: REST API bridge between frontend and worker processes
- **Key Features**:
  - Task lifecycle management via DynamoDB
  - S3 presigned URL generation for secure file uploads
  - Dynamic prompt template loading
  - Comprehensive error handling and validation
  - API documentation with OpenAPI/Swagger

### 3. Worker Service (Python)
- **Location**: `worker/`
- **Technology**: Python with threading, AWS Bedrock, pandas
- **Purpose**: Distributed task processing for compatibility analysis
- **Key Features**:
  - DynamoDB task queue monitoring
  - Concurrent LLM processing with Claude models
  - Image processing from Amazon marketplaces
  - Result generation and S3 storage
  - Progress tracking and error recovery

## Data Flow Architecture

### Complete Request Flow
1. **User Interaction**: User submits task via React frontend
2. **File Upload**: Frontend uploads data file directly to S3 using presigned URL
3. **Task Creation**: Backend API stores task in DynamoDB with "waiting" status
4. **Worker Processing**: Worker service picks up task and processes with Claude LLM
5. **Result Storage**: Results saved to S3 and task marked "completed"
6. **User Access**: Frontend provides download links for completed analysis

### Task Status Lifecycle
- **waiting** → **processing** → **completed**/**failed**

## Core Components

### Shared Source Code (`src/`)
- **call_llm.py**: LLM class with AWS Bedrock integration for multiple Claude models
- **build_prompt.py**: Compatibility criteria definitions and prompt generation for 26 product pairs
- **utils.py**: Data processing utilities, sampling, and calculation functions
- **constant.py**: Model name to ID mappings for AWS Bedrock
- **crawl_images.py**: Amazon marketplace image collection and S3 upload

### Frontend Components (`frontend/src/`)
- **App.tsx**: Main application with React Router and navigation
- **TaskSubmissionForm.tsx**: Task creation interface with file upload
- **TaskViewPage.tsx**: Task monitoring and result management
- **api.ts**: API service layer for backend communication
- **types/**: TypeScript definitions for all data models

### Backend Components (`backend/`)
- **app.py**: FastAPI application with middleware and exception handling
- **models.py**: Pydantic models for request/response validation
- **routes/**: API endpoints (tasks, upload, prompts, pt-pairs)
- **core/config.py**: Environment-based configuration management

### Worker Components (`worker/`)
- **listener.py**: DynamoDB task monitoring and orchestration
- **task_processor.py**: Individual task processing pipeline
- **config.py**: Worker configuration and concurrency settings
- **start_worker.py**: Worker service initialization

## AWS Integration

### DynamoDB Tables
- **auto-in-scope-tasks**: Task lifecycle and status management
- **claude_account**: LLM credentials and account management

### S3 Buckets
- **auto-in-scope**: User uploads and result storage
- **compatibility-data-gen**: Product image storage for LLM analysis

### AWS Bedrock
- **Claude Models**: Primary use of Claude 3.5 Sonnet V2 with fallback models
- **Multi-Account**: Load balancing across multiple AWS accounts and regions
- **Retry Logic**: Comprehensive error handling and retry mechanisms

## Technology Stack

### Frontend
- **React 19.1.0** with TypeScript
- **Ant Design 5.26.3** for UI components
- **React Router 7.6.3** for navigation
- **Axios 1.10.0** for API communication
- **AWS Amplify 6.15.2** for deployment

### Backend
- **FastAPI** with async/await support
- **Pydantic** for data validation
- **Boto3** for AWS service integration
- **Uvicorn** as ASGI server

### Worker
- **Python 3** with threading
- **Pandas** for data processing
- **ThreadPoolExecutor** for concurrent processing
- **AWS SDK** for service integration

## Common Commands

### Development Setup
```bash
# Frontend development
cd frontend && npm install && npm start

# Backend development
cd backend && pip install -r requirements.txt && python run_api.py

# Worker development
cd worker && pip install -r requirements.txt && python start_worker.py

# Legacy standalone processing
python main.py
```

### Production Deployment
```bash
# Setup DynamoDB tables
python setup_dynamodb.py

# Deploy frontend to AWS Amplify
cd frontend && npm run build

# Start services with PM2
pm2 start backend/run_api.py --name "auto-in-scope-api"
pm2 start worker/start_worker.py --name "auto-in-scope-worker"
```

## Key Configuration

### Product Type Pairs
26 predefined compatibility pairs including:
- Air Conditioner ↔ HVAC Air Filter
- Digital Camera ↔ Flash Memory
- Cellular Phone ↔ Headphones
- Video Game Console ↔ Flash Memory
- Printer ↔ Inkjet/Laser Toner
- (And 21 more combinations...)

### Supported Marketplaces
- US, UK, DE, FR, CA, IT, ES

### LLM Configuration
- **Primary Model**: Claude 3.5 Sonnet V2
- **Fallback Models**: Claude 3 Sonnet, Claude 3.5 Haiku
- **Concurrency**: Up to 3 concurrent tasks per worker
- **Retry Logic**: Up to 20 retries with model fallback

## File Organization

```
auto_in_scope/
├── frontend/                 # React web application
│   ├── src/components/      # UI components
│   ├── src/services/        # API integration
│   └── src/types/          # TypeScript definitions
├── backend/                 # FastAPI REST API
│   ├── api/                # API routes and models
│   └── core/               # Configuration
├── worker/                  # Task processing service
│   ├── listener.py         # Task monitoring
│   └── task_processor.py   # Processing pipeline
├── src/                     # Shared source code
│   ├── call_llm.py         # LLM integration
│   ├── build_prompt.py     # Compatibility criteria
│   └── utils.py           # Utility functions
└── data/                   # Input/output data
```

## Development Notes

### System Design Patterns
- **Microservices**: Separate frontend, backend, and worker services
- **Event-Driven**: DynamoDB as task queue for decoupled processing
- **Async Processing**: Background task processing with progress tracking
- **Direct Upload**: S3 presigned URLs for efficient file handling

### Performance Characteristics
- **Concurrent Processing**: Multiple tasks processed simultaneously
- **Image Processing**: Parallel image collection from marketplaces
- **Batch Processing**: Efficient LLM calls with progress tracking
- **Resource Management**: Configurable concurrency limits

### Security Features
- **CORS Configuration**: Restricted cross-origin access
- **File Validation**: Type and size validation for uploads
- **Presigned URLs**: Time-limited secure S3 access
- **Input Validation**: Comprehensive request validation

### Monitoring and Observability
- **Real-time Progress**: Task progress tracked in DynamoDB
- **Structured Logging**: Comprehensive logging across all services
- **Error Tracking**: Detailed error reporting and recovery
- **Health Checks**: API health endpoints for monitoring

## API Documentation

### Key Endpoints
- `GET /api/pt-pairs` - Get available product type pairs
- `POST /api/prompt` - Get prompt template for PT pair
- `POST /api/upload/presigned-url` - Generate S3 upload URL
- `POST /api/tasks` - Submit analysis task
- `GET /api/tasks` - Get user's task list
- `GET /api/tasks/{id}` - Get task details

### File Upload Process
1. Frontend requests presigned URL from backend
2. Frontend uploads file directly to S3
3. Frontend submits task with S3 URI
4. Worker downloads and processes file

## Result Format

### Output Files
- **JSON Format**: `{"asin": "...", "claude_response": "...", "in_scope": "yes/no"}`
- **Excel Format**: Structured spreadsheet with compatibility results
- **Metadata**: GV ceiling calculations and processing statistics

### Storage Structure
- Input files: `s3://auto-in-scope/uploads/`
- Results: `s3://auto-in-scope/results/`
- Images: `s3://compatibility-data-gen/in_scope/`