#!/usr/bin/env python3
"""
Deploy Auto In Scope Backend to Elastic Beanstalk + API Gateway
"""

import boto3
import json
import time
import zipfile
import os
from botocore.exceptions import ClientError

def create_deployment_package():
    """Create deployment package for Elastic Beanstalk"""
    print("📦 Creating deployment package...")
    
    backend_dir = "/home/<USER>/projects/auto_in_scope/backend"
    zip_path = "/tmp/auto-in-scope-backend.zip"
    
    # Create zip file
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add all Python files and directories
        for root, dirs, files in os.walk(backend_dir):
            # Skip __pycache__ directories
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if file.endswith(('.py', '.txt', '.yml', '.yaml', '.json', '.config', '.sh')):
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, backend_dir)
                    zipf.write(file_path, arc_name)
                    print(f"  Added: {arc_name}")
    
    print(f"✅ Deployment package created: {zip_path}")
    return zip_path

def create_eb_application():
    """Create Elastic Beanstalk application"""
    eb_client = boto3.client('elasticbeanstalk')
    
    app_name = 'auto-in-scope-backend'
    
    try:
        # Check if application exists
        response = eb_client.describe_applications(ApplicationNames=[app_name])
        if response['Applications']:
            print(f"✅ Application {app_name} already exists")
            return app_name
    except ClientError:
        pass
    
    try:
        # Create application
        response = eb_client.create_application(
            ApplicationName=app_name,
            Description='Auto In Scope Compatibility Analysis Backend API'
        )
        print(f"✅ Created Elastic Beanstalk application: {app_name}")
        return app_name
        
    except ClientError as e:
        print(f"❌ Error creating application: {e}")
        return None

def upload_version_to_s3(zip_path):
    """Upload application version to S3"""
    s3_client = boto3.client('s3')
    
    bucket_name = 'auto-in-scope'  # Using existing bucket
    key = f'eb-deployments/auto-in-scope-backend-{int(time.time())}.zip'
    
    try:
        # Upload to S3
        print(f"📤 Uploading to S3: s3://{bucket_name}/{key}")
        s3_client.upload_file(zip_path, bucket_name, key)
        print(f"✅ Uploaded to S3")
        return bucket_name, key
        
    except ClientError as e:
        print(f"❌ Error uploading to S3: {e}")
        return None, None

def create_application_version(app_name, s3_bucket, s3_key):
    """Create application version"""
    eb_client = boto3.client('elasticbeanstalk')
    
    version_label = f"v{int(time.time())}"
    
    try:
        response = eb_client.create_application_version(
            ApplicationName=app_name,
            VersionLabel=version_label,
            Description='Auto In Scope Backend API',
            SourceBundle={
                'S3Bucket': s3_bucket,
                'S3Key': s3_key
            },
            AutoCreateApplication=False,
            Process=True
        )
        
        print(f"✅ Created application version: {version_label}")
        return version_label
        
    except ClientError as e:
        print(f"❌ Error creating application version: {e}")
        return None

def create_eb_environment(app_name, version_label):
    """Create Elastic Beanstalk environment"""
    eb_client = boto3.client('elasticbeanstalk')
    
    env_name = 'auto-in-scope-backend-env'
    
    try:
        # Check if environment exists
        response = eb_client.describe_environments(
            ApplicationName=app_name,
            EnvironmentNames=[env_name]
        )
        
        if response['Environments']:
            env = response['Environments'][0]
            if env['Status'] in ['Ready', 'Updating']:
                print(f"✅ Environment {env_name} already exists: {env['EndpointURL']}")
                return env['EndpointURL']
    except ClientError:
        pass
    
    try:
        # Create environment
        print(f"🚀 Creating Elastic Beanstalk environment...")
        response = eb_client.create_environment(
            ApplicationName=app_name,
            EnvironmentName=env_name,
            Description='Auto In Scope Backend Environment',
            VersionLabel=version_label,
            SolutionStackName='64bit Amazon Linux 2023 v4.3.0 running Python 3.11',
            OptionSettings=[
                {
                    'Namespace': 'aws:autoscaling:launchconfiguration',
                    'OptionName': 'InstanceType',
                    'Value': 't3.small'
                },
                {
                    'Namespace': 'aws:autoscaling:launchconfiguration', 
                    'OptionName': 'IamInstanceProfile',
                    'Value': 'aws-elasticbeanstalk-ec2-role'
                },
                {
                    'Namespace': 'aws:elasticbeanstalk:environment',
                    'OptionName': 'LoadBalancerType',
                    'Value': 'application'
                },
                {
                    'Namespace': 'aws:elasticbeanstalk:environment',
                    'OptionName': 'ServiceRole',
                    'Value': 'aws-elasticbeanstalk-service-role'
                },
                {
                    'Namespace': 'aws:elasticbeanstalk:healthreporting:system',
                    'OptionName': 'SystemType',
                    'Value': 'enhanced'
                }
            ]
        )
        
        env_id = response['EnvironmentId']
        print(f"✅ Environment creation started: {env_id}")
        
        # Wait for environment to be ready
        print("⏳ Waiting for environment to be ready (this may take 5-10 minutes)...")
        waiter = eb_client.get_waiter('environment_ready')
        waiter.wait(
            ApplicationName=app_name,
            EnvironmentNames=[env_name],
            WaiterConfig={'Delay': 30, 'MaxAttempts': 20}
        )
        
        # Get environment URL
        response = eb_client.describe_environments(
            ApplicationName=app_name,
            EnvironmentNames=[env_name]
        )
        
        env_url = response['Environments'][0]['EndpointURL']
        print(f"✅ Environment ready: {env_url}")
        return env_url
        
    except ClientError as e:
        print(f"❌ Error creating environment: {e}")
        return None

def create_api_gateway(backend_url):
    """Create API Gateway for the backend"""
    apigateway = boto3.client('apigateway')
    
    api_name = 'auto-in-scope-api'
    
    try:
        # Check if API exists
        response = apigateway.get_rest_apis()
        for api in response['items']:
            if api['name'] == api_name:
                api_id = api['id']
                print(f"✅ API Gateway {api_name} already exists: {api_id}")
                
                # Get existing endpoint
                deployments = apigateway.get_deployments(restApiId=api_id)
                if deployments['items']:
                    api_url = f"https://{api_id}.execute-api.us-east-1.amazonaws.com/prod"
                    print(f"✅ API Gateway URL: {api_url}")
                    return api_url
        
    except ClientError:
        pass
    
    try:
        # Create REST API
        print("🚀 Creating API Gateway...")
        response = apigateway.create_rest_api(
            name=api_name,
            description='Auto In Scope Backend API Gateway',
            endpointConfiguration={
                'types': ['REGIONAL']
            }
        )
        
        api_id = response['id']
        print(f"✅ Created API Gateway: {api_id}")
        
        # Get root resource ID
        resources = apigateway.get_resources(restApiId=api_id)
        root_id = None
        for resource in resources['items']:
            if resource['path'] == '/':
                root_id = resource['id']
                break
        
        # Create {proxy+} resource for catch-all
        proxy_resource = apigateway.create_resource(
            restApiId=api_id,
            parentId=root_id,
            pathPart='{proxy+}'
        )
        proxy_resource_id = proxy_resource['id']
        
        # Create ANY method for proxy resource
        apigateway.put_method(
            restApiId=api_id,
            resourceId=proxy_resource_id,
            httpMethod='ANY',
            authorizationType='NONE',
            requestParameters={
                'method.request.path.proxy': True
            }
        )
        
        # Set up integration
        integration_uri = f"http://{backend_url}/{{proxy}}"
        apigateway.put_integration(
            restApiId=api_id,
            resourceId=proxy_resource_id,
            httpMethod='ANY',
            type='HTTP_PROXY',
            integrationHttpMethod='ANY',
            uri=integration_uri,
            requestParameters={
                'integration.request.path.proxy': 'method.request.path.proxy'
            }
        )
        
        # Create OPTIONS method for CORS (root resource)
        apigateway.put_method(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='OPTIONS',
            authorizationType='NONE'
        )
        
        # CORS integration for root
        apigateway.put_integration(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='OPTIONS',
            type='MOCK',
            requestTemplates={
                'application/json': '{"statusCode": 200}'
            }
        )
        
        # Method response for OPTIONS
        apigateway.put_method_response(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='OPTIONS',
            statusCode='200',
            responseParameters={
                'method.response.header.Access-Control-Allow-Headers': False,
                'method.response.header.Access-Control-Allow-Methods': False,
                'method.response.header.Access-Control-Allow-Origin': False
            }
        )
        
        # Integration response for OPTIONS  
        apigateway.put_integration_response(
            restApiId=api_id,
            resourceId=root_id,
            httpMethod='OPTIONS',
            statusCode='200',
            responseParameters={
                'method.response.header.Access-Control-Allow-Headers': "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
                'method.response.header.Access-Control-Allow-Methods': "'GET,POST,PUT,DELETE,OPTIONS'",
                'method.response.header.Access-Control-Allow-Origin': "'*'"
            }
        )
        
        # Deploy API
        deployment = apigateway.create_deployment(
            restApiId=api_id,
            stageName='prod',
            description='Production deployment'
        )
        
        api_url = f"https://{api_id}.execute-api.us-east-1.amazonaws.com/prod"
        print(f"✅ API Gateway deployed: {api_url}")
        
        return api_url
        
    except ClientError as e:
        print(f"❌ Error creating API Gateway: {e}")
        return None

def main():
    """Main deployment function"""
    print("🚀 Starting Elastic Beanstalk + API Gateway deployment...")
    
    # Step 1: Create deployment package
    zip_path = create_deployment_package()
    if not zip_path:
        return
    
    # Step 2: Create EB application
    app_name = create_eb_application()
    if not app_name:
        return
    
    # Step 3: Upload to S3
    s3_bucket, s3_key = upload_version_to_s3(zip_path)
    if not s3_bucket:
        return
    
    # Step 4: Create application version
    version_label = create_application_version(app_name, s3_bucket, s3_key)
    if not version_label:
        return
    
    # Step 5: Create environment
    backend_url = create_eb_environment(app_name, version_label)
    if not backend_url:
        return
    
    # Step 6: Create API Gateway
    api_url = create_api_gateway(backend_url)
    if not api_url:
        return
    
    # Summary
    print("\n" + "="*60)
    print("🎉 Deployment Complete!")
    print("="*60)
    print(f"Elastic Beanstalk URL: http://{backend_url}")
    print(f"API Gateway URL: {api_url}")
    print(f"API Endpoints:")
    print(f"  - Health Check: {api_url}/health")
    print(f"  - PT Pairs: {api_url}/api/pt-pairs")
    print(f"  - Tasks: {api_url}/api/tasks")
    print("\n📝 Next steps:")
    print(f"1. Update frontend REACT_APP_API_URL to: {api_url}/api")
    print(f"2. Test the API: curl {api_url}/health")
    print(f"3. Deploy frontend to Amplify")
    
    # Cleanup
    os.remove(zip_path)
    print(f"\n🧹 Cleaned up temporary files")

if __name__ == "__main__":
    main()